class AgentAssignment {
  intent "assign_agent"
  claimId string @description("Unique claim identifier")
  agentType "claims_adjuster" | "senior_adjuster" | "fraud_investigator" | "manager" | "legal_counsel" @description("Type of agent to assign")
  priority "low" | "medium" | "high" | "urgent" @description("Assignment priority")
  reason string @description("Reason for assignment")
  estimatedProcessingTime string @description("Estimated time for processing")
}

class AgentDecision {
  intent "agent_decision"
  claimId string @description("Unique claim identifier")
  decision "approve" | "deny" | "investigate" | "request_more_info" | "escalate" @description("Agent's decision")
  settlementAmount float? @description("Settlement amount if approved")
  reasoning string @description("Detailed reasoning for decision")
  nextSteps string[] @description("Recommended next steps")
  requiresManagerApproval bool @description("Whether manager approval is required")
}

class RequestMoreInformation {
  intent "request_more_info"
  claimId string @description("Unique claim identifier")
  requestedDocuments string[] @description("List of documents needed")
  questions string[] @description("Specific questions for the claimant")
  deadline string @description("Deadline for providing information")
  urgency "low" | "medium" | "high" | "urgent" @description("Urgency of the request")
}

class EscalateClaim {
  intent "escalate_claim"
  claimId string @description("Unique claim identifier")
  escalationReason string @description("Reason for escalation")
  escalationLevel "manager" | "director" | "legal" | "fraud_team" @description("Escalation level")
  urgency "high" | "urgent" @description("Escalation urgency")
  additionalContext string @description("Additional context for escalation")
}

class UpdateClaimStatus {
  intent "update_status"
  claimId string @description("Unique claim identifier")
  newStatus "received" | "under_review" | "pending_documents" | "approved" | "denied" | "investigation" | "closed" @description("New claim status")
  statusNotes string @description("Notes about the status change")
  notifyCustomer bool @description("Whether to notify the customer")
  internalNotes string? @description("Internal notes not shared with customer")
}

class CustomerCommunication {
  intent "customer_communication"
  claimId string @description("Unique claim identifier")
  communicationType "acknowledgment" | "status_update" | "decision_notification" | "document_request" @description("Type of communication")
  message string @description("Message content")
  attachments string[] @description("Attachments to include")
  urgency "low" | "medium" | "high" | "urgent" @description("Communication urgency")
}

class TeamNotification {
  intent "team_notification"
  claimId string @description("Unique claim identifier")
  notificationType "new_claim" | "agent_assigned" | "decision_made" | "escalation" | "urgent_alert" @description("Type of notification")
  channel "slack" | "email" | "both" @description("Notification channel")
  message string @description("Notification message")
  recipients string[] @description("List of recipients")
  requiresAction bool @description("Whether action is required")
}

class DocumentProcessing {
  intent "process_documents"
  claimId string @description("Unique claim identifier")
  documentType "police_report" | "medical_record" | "receipt" | "photo" | "insurance_certificate" | "other" @description("Type of document")
  processingAction "ocr" | "classify" | "validate" | "extract_data" @description("Processing action needed")
  priority "low" | "medium" | "high" | "urgent" @description("Processing priority")
  expectedOutput string @description("Expected output from processing")
}

class FraudInvestigation {
  intent "fraud_investigation"
  claimId string @description("Unique claim identifier")
  investigationType "preliminary" | "detailed" | "surveillance" | "background_check" @description("Type of investigation")
  redFlags string[] @description("Red flags identified")
  investigationSteps string[] @description("Steps for investigation")
  estimatedDuration string @description("Estimated investigation duration")
  requiresExternalResources bool @description("Whether external resources are needed")
}

class WorkflowCompletion {
  intent "workflow_complete"
  claimId string @description("Unique claim identifier")
  finalStatus "approved" | "denied" | "closed" | "referred" @description("Final claim status")
  summary string @description("Summary of the claim processing")
  lessonsLearned string[] @description("Lessons learned from this claim")
  recommendations string[] @description("Recommendations for similar claims")
}

type HumanTools = AgentAssignment | AgentDecision | RequestMoreInformation | EscalateClaim | UpdateClaimStatus | CustomerCommunication | TeamNotification | DocumentProcessing | FraudInvestigation | WorkflowCompletion

function DetermineHumanAction(
  claimData: string,
  currentStatus: string,
  aiAnalysis: string,
  availableAgents: string[]
) -> HumanTools {
  client OpenAIGPT4O
  
  prompt #"
    You are a workflow orchestrator for an insurance claims processing system.
    
    Based on the current claim data, status, AI analysis, and available agents, determine the next human action needed.
    
    Claim Data:
    {{ claimData }}
    
    Current Status:
    {{ currentStatus }}
    
    AI Analysis:
    {{ aiAnalysis }}
    
    Available Agents:
    {{ availableAgents }}
    
    Determine the most appropriate human action from the following options:
    1. assign_agent - Assign a specific agent to handle the claim
    2. agent_decision - Make a decision on the claim
    3. request_more_info - Request additional information from the claimant
    4. escalate_claim - Escalate to a higher level
    5. update_status - Update the claim status
    6. customer_communication - Send communication to the customer
    7. team_notification - Notify the team
    8. process_documents - Process additional documents
    9. fraud_investigation - Initiate fraud investigation
    10. workflow_complete - Complete the workflow
    
    Consider the urgency, complexity, and requirements of the claim when making your decision.
    
    {{ ctx.output_format }}
  "#
}

class AgentResponse {
  agentId string @description("ID of the responding agent")
  responseType "approval" | "denial" | "request_info" | "escalation" | "status_update" @description("Type of response")
  decision string @description("Agent's decision or response")
  reasoning string @description("Reasoning for the decision")
  nextActions string[] @description("Recommended next actions")
  confidenceLevel "low" | "medium" | "high" @description("Agent's confidence in the decision")
}

function ProcessAgentResponse(
  claimId: string,
  agentResponse: string,
  claimContext: string
) -> AgentResponse {
  client OpenAIGPT4O
  
  prompt #"
    Process an agent's response to a claim and structure it appropriately.
    
    Claim ID: {{ claimId }}
    Agent Response: {{ agentResponse }}
    Claim Context: {{ claimContext }}
    
    Extract and structure the agent's response including:
    - Response type (approval, denial, request for info, etc.)
    - Decision made
    - Reasoning provided
    - Recommended next actions
    - Confidence level in the decision
    
    {{ ctx.output_format }}
  "#
} 