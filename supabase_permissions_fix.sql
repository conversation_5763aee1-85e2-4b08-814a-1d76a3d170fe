-- Supabase Permissions Fix
-- Run this in Supabase SQL Editor to fix permission issues

-- =============================================================================
-- DISABLE ROW LEVEL SECURITY FOR DEVELOPMENT
-- =============================================================================

-- Disable RLS on all tables for easier development
ALTER TABLE claims DISABLE ROW LEVEL SECURITY;
ALTER TABLE inquiries DISABLE ROW LEVEL SECURITY;
ALTER TABLE claim_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE claim_timeline DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- =============================================================================
-- GRANT PERMISSIONS TO ANON ROLE
-- =============================================================================

-- Grant all permissions to anon role (for API access)
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon;

-- Grant usage on custom types
GRANT USAGE ON TYPE claim_status TO anon;
GRANT USAGE ON TYPE notification_channel TO anon;
GRANT USAGE ON TYPE event_type TO anon;
GRANT USAGE ON TYPE document_type TO anon;

-- =============================================================================
-- GRANT PERMISSIONS TO AUTHENTICATED ROLE
-- =============================================================================

-- Grant all permissions to authenticated role
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant usage on custom types
GRANT USAGE ON TYPE claim_status TO authenticated;
GRANT USAGE ON TYPE notification_channel TO authenticated;
GRANT USAGE ON TYPE event_type TO authenticated;
GRANT USAGE ON TYPE document_type TO authenticated;

-- =============================================================================
-- COMPLETION MESSAGE
-- =============================================================================

SELECT 'Permissions fixed! RLS disabled for development. ✅' as status;
