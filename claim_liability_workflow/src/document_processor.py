"""
Document Processor Service
Handles OCR processing using Zurich OCR API
"""

import asyncio
import logging
import aiohttp
import aiofiles
import os
from typing import Dict, List, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Processes documents using Zurich OCR API"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.ocr_api_url = config.get('zurich_ocr_api_url', 'https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process')
        self.session = None
        
        logger.info("Document processor initialized")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def process_document(self, attachment: Dict[str, Any]) -> str:
        """Process a document using OCR"""
        try:
            logger.info(f"DEBUG: Starting process_document for attachment: {attachment}")

            file_path = attachment.get('temp_path')
            logger.info(f"DEBUG: File path from attachment: {file_path}")

            if not file_path:
                logger.error(f"DEBUG: No temp_path found in attachment: {attachment}")
                return ""

            if not Path(file_path).exists():
                logger.error(f"DEBUG: File not found at path: {file_path}")
                logger.error(f"DEBUG: Current working directory: {os.getcwd()}")
                logger.error(f"DEBUG: Directory contents: {os.listdir('.')}")
                return ""

            # Check file size and permissions
            file_size = os.path.getsize(file_path)
            logger.info(f"DEBUG: File size: {file_size} bytes")
            logger.info(f"DEBUG: File readable: {os.access(file_path, os.R_OK)}")

            logger.info(f"DEBUG: Processing document: {attachment.get('filename', 'unknown')}")

            # Prepare OCR request
            ocr_config = {
                "ocr_engine": "google",
                "google_processor": "OCR_PROCESSOR",
                "llm_routing_enabled": True,
                "post_processing": "v2",
                "preprocessing": "none",
                "parallel_processing": True
            }
            logger.info(f"DEBUG: OCR config: {ocr_config}")

            # Create multipart form data
            logger.info(f"DEBUG: Getting HTTP session")
            session = await self._get_session()
            logger.info(f"DEBUG: HTTP session obtained")

            logger.info(f"DEBUG: Opening file for reading: {file_path}")

            # Read file content first
            with open(file_path, 'rb') as f:
                file_content = f.read()
                logger.info(f"DEBUG: Read {len(file_content)} bytes from file")

            # Create multipart form data for aiohttp
            data = aiohttp.FormData()

            # Add config as JSON string (matching the curl format)
            import json
            config_json = json.dumps(ocr_config)
            data.add_field('config', config_json)
            logger.info(f"DEBUG: Added config field: {config_json}")

            # Add file content with proper filename
            filename = attachment.get('filename', 'document.txt')
            data.add_field('files', file_content, filename=filename, content_type='application/octet-stream')
            logger.info(f"DEBUG: Added file field with filename: {filename}")

            logger.info(f"DEBUG: Making POST request to OCR API: {self.ocr_api_url}")
            async with session.post(
                self.ocr_api_url,
                data=data
            ) as response:
                    logger.info(f"DEBUG: OCR API response status: {response.status}")

                    if response.status == 200:
                        logger.info(f"DEBUG: OCR API request successful, parsing JSON response")
                        result = await response.json()
                        logger.info(f"DEBUG: OCR API response: {result}")

                        ocr_text = self._extract_ocr_text(result)
                        logger.info(f"DEBUG: Extracted OCR text length: {len(ocr_text) if ocr_text else 0}")
                        logger.info(f"DEBUG: OCR text preview: {ocr_text[:200]}..." if ocr_text else "DEBUG: No OCR text extracted")

                        logger.info(f"DEBUG: OCR processing completed for {attachment.get('filename')}")
                        return ocr_text
                    else:
                        error_text = await response.text()
                        logger.error(f"DEBUG: OCR processing failed with status {response.status}")
                        logger.error(f"DEBUG: Error response: {error_text}")
                        return ""

        except Exception as e:
            logger.error(f"DEBUG: Error in process_document: {e}")
            import traceback
            logger.error(f"DEBUG: Traceback: {traceback.format_exc()}")
            return ""
    
    async def process_multiple_documents(self, attachments: List[Dict[str, Any]]) -> List[str]:
        """Process multiple documents in parallel"""
        try:
            tasks = [self.process_document(attachment) for attachment in attachments]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Filter out exceptions and empty results
            ocr_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing document {i}: {result}")
                elif result:
                    ocr_results.append(result)
            
            return ocr_results
            
        except Exception as e:
            logger.error(f"Error processing multiple documents: {e}")
            return []
    
    def _extract_ocr_text(self, ocr_result: Dict[str, Any]) -> str:
        """Extract text from OCR result"""
        try:
            # Extract text from OCR result based on Zurich API response format
            if 'results' in ocr_result:
                text_parts = []
                for result in ocr_result['results']:
                    if 'text' in result:
                        text_parts.append(result['text'])
                return '\n'.join(text_parts)
            elif 'text' in ocr_result:
                return ocr_result['text']
            else:
                logger.warning("Unexpected OCR result format")
                return str(ocr_result)
                
        except Exception as e:
            logger.error(f"Error extracting OCR text: {e}")
            return ""
    
    async def classify_document(self, filename: str, content: str) -> str:
        """Classify document type based on filename and content"""
        try:
            # Simple classification based on filename and content
            filename_lower = filename.lower()
            content_lower = content.lower()
            
            if any(keyword in filename_lower for keyword in ['police', 'report', 'incident']):
                return "police_report"
            elif any(keyword in filename_lower for keyword in ['medical', 'health', 'doctor', 'hospital']):
                return "medical_record"
            elif any(keyword in filename_lower for keyword in ['receipt', 'invoice', 'bill']):
                return "receipt"
            elif any(keyword in filename_lower for keyword in ['photo', 'image', 'jpg', 'jpeg', 'png']):
                return "photo"
            elif any(keyword in filename_lower for keyword in ['insurance', 'certificate', 'policy']):
                return "insurance_certificate"
            else:
                return "other"
                
        except Exception as e:
            logger.error(f"Error classifying document: {e}")
            return "other"
    
    async def validate_document(self, document_type: str, content: str) -> Dict[str, Any]:
        """Validate document completeness and quality"""
        try:
            validation_result = {
                "is_valid": True,
                "completeness_score": 1.0,
                "quality_score": 1.0,
                "issues": []
            }
            
            # Check content length
            if len(content.strip()) < 10:
                validation_result["is_valid"] = False
                validation_result["completeness_score"] = 0.0
                validation_result["issues"].append("Document content too short")
            
            # Check for required fields based on document type
            if document_type == "police_report":
                required_fields = ["incident", "date", "location", "officer"]
                for field in required_fields:
                    if field not in content.lower():
                        validation_result["issues"].append(f"Missing required field: {field}")
                        validation_result["completeness_score"] -= 0.2
            
            elif document_type == "medical_record":
                required_fields = ["patient", "diagnosis", "treatment", "date"]
                for field in required_fields:
                    if field not in content.lower():
                        validation_result["issues"].append(f"Missing required field: {field}")
                        validation_result["completeness_score"] -= 0.2
            
            # Update validation result
            if validation_result["issues"]:
                validation_result["is_valid"] = False
                validation_result["completeness_score"] = max(0.0, validation_result["completeness_score"])
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating document: {e}")
            return {
                "is_valid": False,
                "completeness_score": 0.0,
                "quality_score": 0.0,
                "issues": [f"Validation error: {str(e)}"]
            }
    
    async def extract_structured_data(self, content: str, document_type: str) -> Dict[str, Any]:
        """Extract structured data from document content"""
        try:
            extracted_data = {}
            
            # Extract dates
            import re
            date_patterns = [
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\d{4}-\d{2}-\d{2}',
                r'\d{1,2}-\d{1,2}-\d{4}'
            ]
            
            for pattern in date_patterns:
                dates = re.findall(pattern, content)
                if dates:
                    extracted_data['dates'] = dates
                    break
            
            # Extract amounts (currency)
            amount_pattern = r'\$[\d,]+\.?\d*'
            amounts = re.findall(amount_pattern, content)
            if amounts:
                extracted_data['amounts'] = amounts
            
            # Extract policy numbers
            policy_pattern = r'[A-Z]{2,4}-\d{4}-\d{6}'
            policies = re.findall(policy_pattern, content)
            if policies:
                extracted_data['policy_numbers'] = policies
            
            # Extract phone numbers
            phone_pattern = r'\(\d{3}\) \d{3}-\d{4}|\d{3}-\d{3}-\d{4}'
            phones = re.findall(phone_pattern, content)
            if phones:
                extracted_data['phone_numbers'] = phones
            
            # Extract email addresses
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, content)
            if emails:
                extracted_data['email_addresses'] = emails
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting structured data: {e}")
            return {}
    
    async def test_connection(self) -> bool:
        """Test OCR API connection"""
        try:
            session = await self._get_session()
            
            # Simple test request
            test_data = {'test': 'connection'}
            
            async with session.post(self.ocr_api_url, json=test_data) as response:
                # Even if it fails, we know the endpoint is reachable
                logger.info(f"OCR API connection test: {response.status}")
                return response.status < 500  # Consider 4xx errors as connection success
                
        except Exception as e:
            logger.error(f"OCR API connection test failed: {e}")
            return False 