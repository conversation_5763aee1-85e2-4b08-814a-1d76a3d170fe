# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Literal, Annotated, TypeAlias

from ..._utils import PropertyInfo
from ..._models import BaseModel
from .beta_text_block import BetaText<PERSON>lock
from .beta_thinking_block import <PERSON>Thinking<PERSON>lock
from .beta_tool_use_block import <PERSON>Tool<PERSON>se<PERSON>lock
from .beta_mcp_tool_use_block import <PERSON><PERSON>PToolUseBlock
from .beta_mcp_tool_result_block import BetaMCPToolResultBlock
from .beta_server_tool_use_block import BetaServerToolUseBlock
from .beta_container_upload_block import <PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>
from .beta_redacted_thinking_block import BetaRedactedThinking<PERSON>lock
from .beta_web_search_tool_result_block import BetaWebSearchToolResultBlock
from .beta_code_execution_tool_result_block import BetaCodeExecutionToolResultBlock

__all__ = ["BetaRawContentBlockStartEvent", "ContentBlock"]

ContentBlock: TypeAlias = Annotated[
    Union[
        BetaTextBlock,
        <PERSON>ToolUse<PERSON>lock,
        BetaServerTool<PERSON>seBlock,
        BetaWebSearchToolResultBlock,
        BetaCodeExecutionToolResult<PERSON>lock,
        BetaMCPToolUseBlock,
        BetaMCPToolResultBlock,
        BetaContainerUploadBlock,
        BetaThinkingBlock,
        BetaRedactedThinkingBlock,
    ],
    PropertyInfo(discriminator="type"),
]


class BetaRawContentBlockStartEvent(BaseModel):
    content_block: ContentBlock
    """Response model for a file uploaded to the container."""

    index: int

    type: Literal["content_block_start"]
