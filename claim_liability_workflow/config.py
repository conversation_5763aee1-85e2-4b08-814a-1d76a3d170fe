"""
Configuration for Claims Liability Workflow System
Loads environment variables and provides configuration settings
"""

import os
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class DatabaseConfig:
    """Database configuration"""
    supabase_url: str
    supabase_anon_key: str
    supabase_service_role_key: str

@dataclass
class EmailConfig:
    """Email configuration"""
    claims_email: str
    claims_email_password: str
    imap_server: str
    imap_port: int

@dataclass
class AIConfig:
    """AI services configuration"""
    openai_api_key: str
    zurich_ocr_api_url: str

@dataclass
class HumanLayerConfig:
    """HumanLayer configuration"""
    humanlayer_api_key: str

@dataclass
class ZendeskConfig:
    """Zendesk configuration"""
    zendesk_subdomain: str
    zendesk_email: str
    zendesk_api_token: str

@dataclass
class SlackConfig:
    """Slack configuration"""
    slack_claims_channel: str

@dataclass
class AppConfig:
    """Application configuration"""
    tracking_url: str
    log_level: str
    environment: str

class Config:
    """Main configuration class"""
    
    def __init__(self):
        # Database Configuration
        self.database = DatabaseConfig(
            supabase_url=os.getenv('SUPABASE_URL', 'https://tlduggpohclrgxbvuzhd.supabase.co'),
            supabase_anon_key=os.getenv('SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk'),
            supabase_service_role_key=os.getenv('SUPABASE_SERVICE_ROLE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk')
        )
        
        # Email Configuration
        self.email = EmailConfig(
            claims_email=os.getenv('EMAIL', '<EMAIL>'),
            claims_email_password=os.getenv('CLAIMS_EMAIL_PASSWORD', 'zgyqdymnzqetkvf'),
            imap_server=os.getenv('IMAP_SERVER', 'imap.gmail.com'),
            imap_port=int(os.getenv('IMAP_PORT', '993'))
        )
        
        # AI Configuration
        self.ai = AIConfig(
            openai_api_key=os.getenv('OPENAI_API_KEY', '***************************************************'),
            zurich_ocr_api_url=os.getenv('ZURICH_OCR_API_URL', 'https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process')
        )
        
        # HumanLayer Configuration
        self.humanlayer = HumanLayerConfig(
            humanlayer_api_key=os.getenv('HUMANLAYER_API_KEY', 'hl-a-XCV49qIfZRLqh8N1ra8ZhiQuYIXdXRpI1cGHRK052w0')
        )
        
        # Zendesk Configuration
        self.zendesk = ZendeskConfig(
            zendesk_subdomain=os.getenv('ZENDESK_SUBDOMAIN', 'd3v-rozieai5417'),
            zendesk_email=os.getenv('ZENDESK_EMAIL', '<EMAIL>'),
            zendesk_api_token=os.getenv('ZENDESK_API_TOKEN', '1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1')
        )
        
        # Slack Configuration
        self.slack = SlackConfig(
            slack_claims_channel=os.getenv('SLACK_CLAIMS_CHANNEL', 'C092M4E1SH0')
        )
        
        # Application Configuration
        self.app = AppConfig(
            tracking_url=os.getenv('TRACKING_URL', 'https://claims.rozie.ai'),
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            environment=os.getenv('ENVIRONMENT', 'development')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            # Database
            'supabase_url': self.database.supabase_url,
            'supabase_anon_key': self.database.supabase_anon_key,
            'supabase_service_role_key': self.database.supabase_service_role_key,
            
            # Email
            'claims_email': self.email.claims_email,
            'claims_email_password': self.email.claims_email_password,
            'imap_server': self.email.imap_server,
            'imap_port': self.email.imap_port,
            
            # AI
            'openai_api_key': self.ai.openai_api_key,
            'zurich_ocr_api_url': self.ai.zurich_ocr_api_url,
            
            # HumanLayer
            'humanlayer_api_key': self.humanlayer.humanlayer_api_key,
            
            # Zendesk
            'zendesk_subdomain': self.zendesk.zendesk_subdomain,
            'zendesk_email': self.zendesk.zendesk_email,
            'zendesk_api_token': self.zendesk.zendesk_api_token,
            
            # Slack
            'slack_claims_channel': self.slack.slack_claims_channel,
            
            # App
            'tracking_url': self.app.tracking_url,
            'log_level': self.app.log_level,
            'environment': self.app.environment
        }
    
    def validate(self) -> bool:
        """Validate configuration"""
        required_fields = [
            self.database.supabase_url,
            self.database.supabase_anon_key,
            self.email.claims_email,
            self.email.claims_email_password,
            self.ai.openai_api_key,
            self.humanlayer.humanlayer_api_key,
            self.zendesk.zendesk_subdomain,
            self.zendesk.zendesk_email,
            self.zendesk.zendesk_api_token
        ]
        
        return all(field for field in required_fields)
    
    def get_missing_fields(self) -> list:
        """Get list of missing configuration fields"""
        missing = []
        
        if not self.database.supabase_url:
            missing.append('SUPABASE_URL')
        if not self.database.supabase_anon_key:
            missing.append('SUPABASE_ANON_KEY')
        if not self.email.claims_email:
            missing.append('EMAIL')
        if not self.email.claims_email_password:
            missing.append('CLAIMS_EMAIL_PASSWORD')
        if not self.ai.openai_api_key:
            missing.append('OPENAI_API_KEY')
        if not self.humanlayer.humanlayer_api_key:
            missing.append('HUMANLAYER_API_KEY')
        if not self.zendesk.zendesk_subdomain:
            missing.append('ZENDESK_SUBDOMAIN')
        if not self.zendesk.zendesk_email:
            missing.append('ZENDESK_EMAIL')
        if not self.zendesk.zendesk_api_token:
            missing.append('ZENDESK_API_TOKEN')
        
        return missing

# Global configuration instance
config = Config() 