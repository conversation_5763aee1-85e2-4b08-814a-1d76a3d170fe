-- Add missing columns to claims table
-- Run this in Supabase SQL Editor

-- Add the missing JSON columns that the application expects
ALTER TABLE claims ADD COLUMN IF NOT EXISTS email_data JSONB DEFAULT '{}';
ALTER TABLE claims ADD COLUMN IF NOT EXISTS classification JSONB DEFAULT '{}';
ALTER TABLE claims ADD COLUMN IF NOT EXISTS extracted_details JSONB DEFAULT '{}';
ALTER TABLE claims ADD COLUMN IF NOT EXISTS ai_analysis JSONB DEFAULT '{}';
ALTER TABLE claims ADD COLUMN IF NOT EXISTS decision JSONB DEFAULT NULL;

-- Update the sample claim with some basic data so the application doesn't error
UPDATE claims 
SET 
    email_data = '{"from_email": "<EMAIL>", "subject": "Sample Claim", "body": "This is a sample claim"}',
    classification = '{"claim_type": "auto_liability", "severity": "medium"}',
    extracted_details = '{"incident_location": "Sample Location", "incident_time": "2025-06-24"}',
    ai_analysis = '{"confidence": 0.85, "recommendation": "approve"}'
WHERE claim_number = 'CLAIM-2025-001';

-- Completion message
SELECT 'Missing columns added successfully! ✅' as status;
