###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, Union, cast
from typing_extensions import Literal

import baml_py

from . import _baml
from .types import Checked, Check


class LlmResponseParser:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaim(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.ClaimAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeClaim",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.ClaimAnalysis, parsed)
    
    def ClassifyEmail(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.EmailClassification:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyEmail",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.EmailClassification, parsed)
    
    def DetermineHumanAction(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> Union[_baml.types.AgentAssignment, _baml.types.AgentDecision, _baml.types.RequestMoreInformation, _baml.types.EscalateClaim, _baml.types.UpdateClaimStatus, _baml.types.CustomerCommunication, _baml.types.TeamNotification, _baml.types.DocumentProcessing, _baml.types.FraudInvestigation, _baml.types.WorkflowCompletion]:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "DetermineHumanAction",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(Union[_baml.types.AgentAssignment, _baml.types.AgentDecision, _baml.types.RequestMoreInformation, _baml.types.EscalateClaim, _baml.types.UpdateClaimStatus, _baml.types.CustomerCommunication, _baml.types.TeamNotification, _baml.types.DocumentProcessing, _baml.types.FraudInvestigation, _baml.types.WorkflowCompletion], parsed)
    
    def ExtractClaimDetails(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.DocumentExtraction:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractClaimDetails",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.DocumentExtraction, parsed)
    
    def ProcessAgentResponse(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.types.AgentResponse:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ProcessAgentResponse",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        False,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.types.AgentResponse, parsed)
    


class LlmStreamParser:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaim(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.ClaimAnalysis:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "AnalyzeClaim",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.ClaimAnalysis, parsed)
    
    def ClassifyEmail(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.EmailClassification:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ClassifyEmail",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.EmailClassification, parsed)
    
    def DetermineHumanAction(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> Optional[Union[_baml.partial_types.AgentAssignment, _baml.partial_types.AgentDecision, _baml.partial_types.RequestMoreInformation, _baml.partial_types.EscalateClaim, _baml.partial_types.UpdateClaimStatus, _baml.partial_types.CustomerCommunication, _baml.partial_types.TeamNotification, _baml.partial_types.DocumentProcessing, _baml.partial_types.FraudInvestigation, _baml.partial_types.WorkflowCompletion]]:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "DetermineHumanAction",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(Optional[Union[_baml.partial_types.AgentAssignment, _baml.partial_types.AgentDecision, _baml.partial_types.RequestMoreInformation, _baml.partial_types.EscalateClaim, _baml.partial_types.UpdateClaimStatus, _baml.partial_types.CustomerCommunication, _baml.partial_types.TeamNotification, _baml.partial_types.DocumentProcessing, _baml.partial_types.FraudInvestigation, _baml.partial_types.WorkflowCompletion]], parsed)
    
    def ExtractClaimDetails(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.DocumentExtraction:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ExtractClaimDetails",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.DocumentExtraction, parsed)
    
    def ProcessAgentResponse(
        self,
        llm_response: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> _baml.partial_types.AgentResponse:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)

      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      parsed = self.__runtime.parse_llm_response(
        "ProcessAgentResponse",
        llm_response,
        _baml.types,
        _baml.types,
        _baml.partial_types,
        True,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
      )

      return cast(_baml.partial_types.AgentResponse, parsed)
    


__all__ = ["LlmResponseParser", "LlmStreamParser"]