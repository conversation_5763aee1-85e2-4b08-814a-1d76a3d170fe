class ClaimClassification {
  claimType "auto" | "property" | "liability" | "professional" | "bodily_injury" @description("The type of insurance claim")
  severity "minor" | "major" | "catastrophic" @description("Severity level of the claim")
  estimatedValue float @description("Estimated claim value in CAD")
  confidenceScore float @description("AI confidence in the classification (0-1)")
}

class FraudAnalysis {
  riskScore int @description("Fraud risk score from 0-100")
  redFlags string[] @description("List of identified red flags")
  fraudIndicators string[] @description("Specific fraud indicators found")
  recommendation "approve" | "investigate" | "deny" @description("AI recommendation based on fraud analysis")
  confidenceScore float @description("Confidence in fraud assessment (0-1)")
}

class CoverageAnalysis {
  policyValid bool @description("Whether the policy is valid and active")
  coverageType string @description("Type of coverage applicable")
  deductible float @description("Applicable deductible amount")
  coverageLimit float @description("Maximum coverage limit")
  exclusions string[] @description("List of coverage exclusions")
  recommendation "covered" | "partially_covered" | "not_covered" @description("Coverage recommendation")
}

class DocumentAnalysis {
  documentType "police_report" | "medical_record" | "receipt" | "photo" | "insurance_certificate" | "other" @description("Type of document")
  extractedData map<string, string> @description("Key-value pairs of extracted data")
  completeness float @description("Document completeness score (0-1)")
  quality float @description("Document quality score (0-1)")
  requiredForClaim bool @description("Whether this document is required for the claim")
}

class LiabilityAssessment {
  faultDetermination "clear_liability" | "shared_liability" | "no_liability" | "unclear" @description("Liability determination")
  faultPercentage float @description("Percentage of fault assigned (0-100)")
  contributingFactors string[] @description("Factors contributing to the incident")
  evidenceStrength "strong" | "moderate" | "weak" @description("Strength of evidence")
  recommendation string @description("AI recommendation for liability")
}

class ClaimAnalysis {
  classification ClaimClassification
  fraudAnalysis FraudAnalysis
  coverageAnalysis CoverageAnalysis
  documents DocumentAnalysis[]
  liabilityAssessment LiabilityAssessment
  overallRecommendation "approve" | "deny" | "investigate" | "request_more_info" @description("Overall AI recommendation")
  reasoning string @description("Detailed reasoning for the recommendation")
  nextSteps string[] @description("Recommended next steps")
  priority "low" | "medium" | "high" | "urgent" @description("Claim priority level")
}

function AnalyzeClaim(
  emailContent: string,
  attachments: string[],
  ocrResults: string[]
) -> ClaimAnalysis {
  client OpenAIGPT4O
  
  prompt #"
    You are an expert insurance claims analyst specializing in Canadian liability claims.
    
    Analyze the following claim information and provide a comprehensive assessment:
    
    Email Content:
    {{ emailContent }}
    
    Attachments:
    {{ attachments }}
    
    OCR Results:
    {{ ocrResults }}
    
    Please provide a detailed analysis including:
    1. Claim classification and severity assessment
    2. Fraud risk analysis with specific indicators
    3. Coverage analysis and policy validation
    4. Document analysis and completeness assessment
    5. Liability assessment and fault determination
    6. Overall recommendation with reasoning
    
    Focus on Canadian insurance regulations and liability laws.
    Be thorough in identifying potential fraud indicators and coverage issues.
    
    {{ ctx.output_format }}
  "#
}

class EmailClassification {
  isClaim bool @description("Whether this email contains a claim")
  claimType "auto" | "property" | "liability" | "professional" | "bodily_injury" | "general_inquiry" | "other" @description("Type of claim or inquiry")
  urgency "low" | "medium" | "high" | "urgent" @description("Urgency level")
  requiresImmediateResponse bool @description("Whether immediate response is needed")
  suggestedResponse string @description("Suggested response template")
}

function ClassifyEmail(
  subject: string,
  body: string,
  hasAttachments: bool
) -> EmailClassification {
  client OpenAIGPT4O
  
  prompt #"
    You are an email classification system for an insurance claims processing company.
    
    Classify the following email to determine if it's a claim and what type:
    
    Subject: {{ subject }}
    Body: {{ body }}
    Has Attachments: {{ hasAttachments }}
    
    Determine:
    1. Is this a claim or general inquiry?
    2. What type of claim (if applicable)?
    3. Urgency level
    4. Whether immediate response is needed
    5. Suggested response template
    
    Focus on Canadian insurance terminology and claim patterns.
    
    {{ ctx.output_format }}
  "#
}

class DocumentExtraction {
  policyNumber string? @description("Extracted policy number")
  claimAmount float? @description("Claim amount mentioned")
  incidentDate string? @description("Date of incident")
  location string? @description("Location of incident")
  partiesInvolved string[] @description("Names of parties involved")
  damages string[] @description("Types of damages mentioned")
  witnesses string[] @description("Witness information")
  policeReportNumber string? @description("Police report number if mentioned")
  medicalInformation string[] @description("Medical details if applicable")
}

function ExtractClaimDetails(
  emailContent: string,
  ocrText: string
) -> DocumentExtraction {
  client OpenAIGPT4O
  
  prompt #"
    Extract key claim details from the provided email content and OCR text.
    
    Email Content:
    {{ emailContent }}
    
    OCR Text:
    {{ ocrText }}
    
    Extract the following information:
    - Policy number
    - Claim amount
    - Incident date
    - Location
    - Parties involved
    - Types of damages
    - Witness information
    - Police report number
    - Medical information (if applicable)
    
    Be thorough and extract all relevant information for claims processing.
    
    {{ ctx.output_format }}
  "#
} 