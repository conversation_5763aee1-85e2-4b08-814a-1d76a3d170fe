# Claims Liability Workflow System - Environment Configuration

# Application Settings
APP_NAME=Claims Liability Workflow
APP_VERSION=1.0.0
APP_ENV=development
LOG_LEVEL=INFO

# Email Configuration (IMAP for monitoring)
EMAIL_HOST=imap.gmail.com
EMAIL_PORT=993
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_USE_SSL=true

# SMTP Configuration (for sending notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_USE_TLS=true

# AI and ML Configuration
OPENAI_API_KEY=your_openai_api_key_here
ZURICH_OCR_API_KEY=your_zurich_ocr_api_key_here
ZURICH_OCR_BASE_URL=https://api.zurich.com/ocr/v1

# HumanLayer Configuration
HUMANLAYER_API_KEY=your_humanlayer_api_key_here
HUMANLAYER_WORKSPACE_ID=your_workspace_id_here
HUMANLAYER_BASE_URL=https://api.humanlayer.com/v1

# Zendesk Configuration
ZENDESK_DOMAIN=your_domain.zendesk.com
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=your_zendesk_api_token_here
ZENDESK_BASE_URL=https://your_domain.zendesk.com/api/v2

# Slack Configuration
SLACK_BOT_TOKEN=xoxb-your_slack_bot_token_here
SLACK_CHANNEL_ID=your_channel_id_here
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url

# Database Configuration (Supabase)
SUPABASE_URL=https://your_project.supabase.co
SUPABASE_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Workflow Configuration
CLAIM_NUMBER_PREFIX=CLM
AUTO_ESCALATION_HOURS=24
MAX_ATTACHMENT_SIZE_MB=25
SUPPORTED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png,tiff

# Notification Settings
SEND_EMAIL_NOTIFICATIONS=true
SEND_SLACK_NOTIFICATIONS=true
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY_SECONDS=300

# Security Settings
ENCRYPTION_KEY=your_32_character_encryption_key_here
SESSION_SECRET=your_session_secret_here
API_RATE_LIMIT=100
API_RATE_LIMIT_WINDOW=3600

# Monitoring and Logging
LOG_FILE_PATH=logs/claims_workflow.log
ERROR_LOG_FILE_PATH=logs/claims_workflow_errors.log
AUDIT_LOG_FILE_PATH=logs/audit.log
LOG_RETENTION_DAYS=30

# Development Settings
DEBUG_MODE=false
TEST_MODE=false
MOCK_EXTERNAL_APIS=false
ENABLE_METRICS=true 