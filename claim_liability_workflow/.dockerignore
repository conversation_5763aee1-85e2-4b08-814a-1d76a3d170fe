# Python
venv/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
ENV/
env/
.venv/
.ENV/

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
claims_workflow.log

# Testing
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Git
.git/
.gitignore

# Docker files
Dockerfile*
docker-compose*.yml

# Temporary files
*.tmp
*.temp
.tmp/

# Database
*.db
*.sqlite
*.sqlite3

# Environment files (will be mounted as volume)
.env.example