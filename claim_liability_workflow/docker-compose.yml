version: '3.8'

services:
  claims-workflow:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: claims-liability-workflow
    restart: unless-stopped
    environment:
      # Database Configuration
      - SUPABASE_URL=${SUPABASE_URL:-https://tlduggpohclrgxbvuzhd.supabase.co}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      
      # Email Configuration
      - EMAIL=${EMAIL:-<EMAIL>}
      - CLAIMS_EMAIL_PASSWORD=${CLAIMS_EMAIL_PASSWORD}
      - IMAP_SERVER=${IMAP_SERVER:-imap.gmail.com}
      - IMAP_PORT=${IMAP_PORT:-993}
      
      # AI Services Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ZURICH_OCR_API_URL=${ZURICH_OCR_API_URL:-https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process}
      
      # HumanLayer Configuration
      - HUMANLAYER_API_KEY=${HUMANLAYER_API_KEY}
      
      # Zendesk Configuration
      - ZENDESK_SUBDOMAIN=${ZENDESK_SUBDOMAIN:-d3v-rozieai5417}
      - ZENDESK_EMAIL=${ZENDESK_EMAIL}
      - ZENDESK_API_TOKEN=${ZENDESK_API_TOKEN}
      
      # Slack Configuration
      - SLACK_CLAIMS_CHANNEL=${SLACK_CLAIMS_CHANNEL:-C092M4E1SH0}
      
      # Application Configuration
      - TRACKING_URL=${TRACKING_URL:-https://claims.rozie.ai}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    
    volumes:
      # Mount logs directory for persistence
      - ./logs:/app/logs
      # Mount .env file if it exists
      - ./.env:/app/.env:ro
    
    ports:
      # Expose port for health checks and potential web interface
      - "8000:8000"
    
    networks:
      - claims-network
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Add a simple web dashboard for monitoring
  dashboard:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: claims-dashboard
    restart: unless-stopped
    command: ["python", "dashboard_app.py"]
    environment:
      # Same environment variables as main service
      - SUPABASE_URL=${SUPABASE_URL:-https://tlduggpohclrgxbvuzhd.supabase.co}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=${ENVIRONMENT:-production}
    
    volumes:
      - ./logs:/app/logs:ro
      - ./.env:/app/.env:ro
    
    ports:
      - "8001:8001"
    
    networks:
      - claims-network
    
    depends_on:
      - claims-workflow
    
    # Health check for dashboard
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  claims-network:
    driver: bridge

volumes:
  logs:
    driver: local
