"""
BAML Types for Claims Liability Workflow
Fallback type definitions when BAML client hasn't been generated.
"""

from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

# Enums for type safety
class ClaimType(str, Enum):
    AUTO = "auto"
    PROPERTY = "property"
    LIABILITY = "liability"
    PROFESSIONAL = "professional"
    BODILY_INJURY = "bodily_injury"

class Severity(str, Enum):
    MINOR = "minor"
    MAJOR = "major"
    CATASTROPHIC = "catastrophic"

class Recommendation(str, Enum):
    APPROVE = "approve"
    DENY = "deny"
    INVESTIGATE = "investigate"
    REQUEST_MORE_INFO = "request_more_info"

class Priority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

# Data classes for structured data
@dataclass
class ClaimClassification:
    claimType: ClaimType
    severity: Severity
    estimatedValue: float
    confidenceScore: float

@dataclass
class FraudAnalysis:
    riskScore: int
    redFlags: List[str]
    fraudIndicators: List[str]
    recommendation: Recommendation
    confidenceScore: float

@dataclass
class CoverageAnalysis:
    policyValid: bool
    coverageType: str
    deductible: float
    coverageLimit: float
    exclusions: List[str]
    recommendation: str

@dataclass
class DocumentAnalysis:
    documentType: str
    extractedData: Dict[str, str]
    completeness: float
    quality: float
    requiredForClaim: bool

@dataclass
class LiabilityAssessment:
    faultDetermination: str
    faultPercentage: float
    contributingFactors: List[str]
    evidenceStrength: str
    recommendation: str

@dataclass
class ClaimAnalysis:
    classification: ClaimClassification
    fraudAnalysis: FraudAnalysis
    coverageAnalysis: CoverageAnalysis
    documents: List[DocumentAnalysis]
    liabilityAssessment: LiabilityAssessment
    overallRecommendation: Recommendation
    reasoning: str
    nextSteps: List[str]
    priority: Priority

@dataclass
class EmailClassification:
    isClaim: bool
    claimType: str
    urgency: str
    requiresImmediateResponse: bool
    suggestedResponse: str

@dataclass
class DocumentExtraction:
    policyNumber: Optional[str]
    claimAmount: Optional[float]
    incidentDate: Optional[str]
    location: Optional[str]
    partiesInvolved: List[str]
    damages: List[str]
    witnesses: List[str]
    policeReportNumber: Optional[str]
    medicalInformation: List[str]

@dataclass
class HumanTools:
    intent: str
    claimId: str
    agentType: str
    priority: str
    reason: str
    estimatedProcessingTime: str

@dataclass
class AgentResponse:
    decision: str
    reasoning: str
    nextSteps: List[str]
    requiresFollowUp: bool

# Function type aliases for compatibility
AnalyzeClaim = ClaimAnalysis
ClassifyEmail = EmailClassification
ExtractClaimDetails = DocumentExtraction
DetermineHumanAction = HumanTools
ProcessAgentResponse = AgentResponse

# Helper functions to convert dict responses to typed objects
def dict_to_claim_analysis(data: Dict[str, Any]) -> ClaimAnalysis:
    """Convert dictionary response to ClaimAnalysis object"""
    try:
        return ClaimAnalysis(
            classification=ClaimClassification(
                claimType=ClaimType(data["classification"]["claimType"]),
                severity=Severity(data["classification"]["severity"]),
                estimatedValue=data["classification"]["estimatedValue"],
                confidenceScore=data["classification"]["confidenceScore"]
            ),
            fraudAnalysis=FraudAnalysis(
                riskScore=data["fraudAnalysis"]["riskScore"],
                redFlags=data["fraudAnalysis"]["redFlags"],
                fraudIndicators=data["fraudAnalysis"]["fraudIndicators"],
                recommendation=Recommendation(data["fraudAnalysis"]["recommendation"]),
                confidenceScore=data["fraudAnalysis"]["confidenceScore"]
            ),
            coverageAnalysis=CoverageAnalysis(
                policyValid=data["coverageAnalysis"]["policyValid"],
                coverageType=data["coverageAnalysis"]["coverageType"],
                deductible=data["coverageAnalysis"]["deductible"],
                coverageLimit=data["coverageAnalysis"]["coverageLimit"],
                exclusions=data["coverageAnalysis"]["exclusions"],
                recommendation=data["coverageAnalysis"]["recommendation"]
            ),
            documents=[
                DocumentAnalysis(
                    documentType=doc.get("documentType", "unknown"),
                    extractedData=doc.get("extractedData", {}),
                    completeness=doc.get("completeness", 0.0),
                    quality=doc.get("quality", 0.0),
                    requiredForClaim=doc.get("requiredForClaim", False)
                ) for doc in data.get("documents", [])
            ],
            liabilityAssessment=LiabilityAssessment(
                faultDetermination=data["liabilityAssessment"]["faultDetermination"],
                faultPercentage=data["liabilityAssessment"]["faultPercentage"],
                contributingFactors=data["liabilityAssessment"]["contributingFactors"],
                evidenceStrength=data["liabilityAssessment"]["evidenceStrength"],
                recommendation=data["liabilityAssessment"]["recommendation"]
            ),
            overallRecommendation=Recommendation(data["overallRecommendation"]),
            reasoning=data["reasoning"],
            nextSteps=data["nextSteps"],
            priority=Priority(data["priority"])
        )
    except (KeyError, ValueError) as e:
        # Return a default analysis if conversion fails
        return ClaimAnalysis(
            classification=ClaimClassification(
                claimType=ClaimType.AUTO,
                severity=Severity.MINOR,
                estimatedValue=0.0,
                confidenceScore=0.0
            ),
            fraudAnalysis=FraudAnalysis(
                riskScore=0,
                redFlags=[],
                fraudIndicators=[],
                recommendation=Recommendation.INVESTIGATE,
                confidenceScore=0.0
            ),
            coverageAnalysis=CoverageAnalysis(
                policyValid=False,
                coverageType="unknown",
                deductible=0.0,
                coverageLimit=0.0,
                exclusions=[],
                recommendation="investigate"
            ),
            documents=[],
            liabilityAssessment=LiabilityAssessment(
                faultDetermination="unclear",
                faultPercentage=0.0,
                contributingFactors=[],
                evidenceStrength="weak",
                recommendation="investigate"
            ),
            overallRecommendation=Recommendation.INVESTIGATE,
            reasoning=f"Error processing claim analysis: {e}",
            nextSteps=["Manual review required"],
            priority=Priority.HIGH
        )

def dict_to_email_classification(data: Dict[str, Any]) -> EmailClassification:
    """Convert dictionary response to EmailClassification object"""
    return EmailClassification(
        isClaim=data.get("isClaim", False),
        claimType=data.get("claimType", "general_inquiry"),
        urgency=data.get("urgency", "medium"),
        requiresImmediateResponse=data.get("requiresImmediateResponse", False),
        suggestedResponse=data.get("suggestedResponse", "Thank you for your inquiry.")
    )

def dict_to_document_extraction(data: Dict[str, Any]) -> DocumentExtraction:
    """Convert dictionary response to DocumentExtraction object"""
    return DocumentExtraction(
        policyNumber=data.get("policyNumber"),
        claimAmount=data.get("claimAmount"),
        incidentDate=data.get("incidentDate"),
        location=data.get("location"),
        partiesInvolved=data.get("partiesInvolved", []),
        damages=data.get("damages", []),
        witnesses=data.get("witnesses", []),
        policeReportNumber=data.get("policeReportNumber"),
        medicalInformation=data.get("medicalInformation", [])
    )

def dict_to_human_tools(data: Dict[str, Any]) -> HumanTools:
    """Convert dictionary response to HumanTools object"""
    return HumanTools(
        intent=data.get("intent", "assign_agent"),
        claimId=data.get("claimId", "unknown"),
        agentType=data.get("agentType", "claims_adjuster"),
        priority=data.get("priority", "medium"),
        reason=data.get("reason", "Standard processing required"),
        estimatedProcessingTime=data.get("estimatedProcessingTime", "2-3 business days")
    )
