# Use official Python 3.11 image (compatible with pydantic and all dependencies)
FROM python:3.11-slim

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install BAML CLI and upgrade pip
RUN pip install --upgrade pip setuptools wheel

# Copy requirements and install Python dependencies
COPY requirements.txt ./

# Install dependencies with compatible versions for Python 3.11
RUN pip install --no-cache-dir \
    aiohttp==3.9.1 \
    python-dotenv==1.0.0 \
    openai==1.3.7 \
    supabase==2.0.2 \
    email-validator==2.1.0 \
    requests==2.31.0 \
    httpx \
    structlog==23.2.0 \
    pydantic>=2.8.0 \
    pydantic-settings>=2.1.0 \
    python-multipart==0.0.6 \
    aiofiles==23.2.1 \
    python-dateutil==2.8.2 \
    orjson==3.9.10 \
    pytest==7.4.3 \
    pytest-asyncio==0.21.1 \
    black==23.12.0 \
    flake8==6.1.0 \
    mypy==1.8.0

# Install BAML Python package
RUN pip install --no-cache-dir baml-py

# Copy BAML source files first
COPY baml_src/ ./baml_src/

# Copy existing baml_client if it exists
COPY baml_client/ ./baml_client/

# Copy the rest of the application code (including generated baml_client)
COPY . .

# Create logs directory
RUN mkdir -p /app/logs

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health')" || exit 1

# Expose port for health checks and potential web interface
EXPOSE 8000

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash app && chown -R app:app /app
USER app

# Entrypoint
CMD ["python", "main.py", "start"]