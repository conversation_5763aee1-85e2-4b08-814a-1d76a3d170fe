###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
__version__ = "0.90.2"

try:
  from baml_py.safe_import import EnsureBamlPyImport
except ImportError:
  raise ImportError(f"""Update to baml-py required.
Version of baml_client generator (see generators.baml): {__version__}

Please upgrade baml-py to version "{__version__}".

$ pip install baml-py=={__version__}
$ uv add baml-py=={__version__}

If nothing else works, please ask for help:

https://github.com/boundaryml/baml/issues
https://boundaryml.com/discord
""") from None

with EnsureBamlPyImport(__version__) as e:
  e.raise_if_incompatible_version(__version__)

  from . import types
  from . import tracing
  from . import partial_types
  from . import config
  from .config import reset_baml_env_vars
  
  from .async_client import b
  

__all__ = [
  "b",
  "partial_types",
  "tracing",
  "types",
  "reset_baml_env_vars",
  "config",
]