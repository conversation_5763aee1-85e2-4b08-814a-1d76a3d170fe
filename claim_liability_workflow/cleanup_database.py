#!/usr/bin/env python3
"""
Database Cleanup Script
Fixes validation errors by updating old claim types to match new BAML schema
"""

import asyncio
import json
import logging
import os
from typing import Dict, Any
from supabase import create_client, Client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supabase configuration
SUPABASE_URL = os.getenv('SUPABASE_URL', 'https://tlduggpohclrgxbvuzhd.supabase.co')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk')

# Claim type mapping from old to new BAML schema
CLAIM_TYPE_MAPPING = {
    'auto_liability': 'liability',
    'general': 'general_inquiry',
    'auto': 'auto',
    'property': 'property',
    'professional': 'professional',
    'bodily_injury': 'bodily_injury',
    'other': 'other'
}

class DatabaseCleanup:
    def __init__(self):
        self.client: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
        
    async def get_all_claims(self):
        """Get all claims from database"""
        try:
            result = self.client.table('claims').select('*').execute()
            return result.data
        except Exception as e:
            logger.error(f"Error getting claims: {e}")
            return []
    
    async def update_claim_classification(self, claim_id: str, classification_data: Dict[str, Any]):
        """Update claim classification data"""
        try:
            # Update the classification JSON field
            result = self.client.table('claims').update({
                'classification': json.dumps(classification_data)
            }).eq('claim_id', claim_id).execute()
            
            if result.data:
                logger.info(f"Updated claim {claim_id} classification")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error updating claim {claim_id}: {e}")
            return False
    
    async def fix_claim_types(self):
        """Fix invalid claim types in all claims"""
        logger.info("Starting database cleanup...")
        
        claims = await self.get_all_claims()
        logger.info(f"Found {len(claims)} claims to process")
        
        updated_count = 0
        error_count = 0
        
        for claim in claims:
            try:
                # Parse classification JSON
                classification_str = claim.get('classification', '{}')
                if isinstance(classification_str, str):
                    classification = json.loads(classification_str)
                else:
                    classification = classification_str
                
                # Get current claim type
                current_claim_type = classification.get('claimType', 'other')
                
                # Check if claim type needs updating
                if current_claim_type in CLAIM_TYPE_MAPPING:
                    new_claim_type = CLAIM_TYPE_MAPPING[current_claim_type]
                    
                    if new_claim_type != current_claim_type:
                        logger.info(f"Updating claim {claim['claim_id']}: '{current_claim_type}' -> '{new_claim_type}'")
                        
                        # Update classification
                        classification['claimType'] = new_claim_type
                        
                        # Update in database
                        success = await self.update_claim_classification(claim['claim_id'], classification)
                        if success:
                            updated_count += 1
                        else:
                            error_count += 1
                    else:
                        logger.info(f"Claim {claim['claim_id']} already has valid claim type: {current_claim_type}")
                else:
                    # Unknown claim type, set to 'other'
                    logger.warning(f"Unknown claim type '{current_claim_type}' in claim {claim['claim_id']}, setting to 'other'")
                    classification['claimType'] = 'other'
                    
                    success = await self.update_claim_classification(claim['claim_id'], classification)
                    if success:
                        updated_count += 1
                    else:
                        error_count += 1
                        
            except Exception as e:
                logger.error(f"Error processing claim {claim.get('claim_id', 'unknown')}: {e}")
                error_count += 1
        
        logger.info(f"Database cleanup completed:")
        logger.info(f"  - Updated: {updated_count} claims")
        logger.info(f"  - Errors: {error_count} claims")
        logger.info(f"  - Total processed: {len(claims)} claims")
        
        return updated_count, error_count

async def main():
    """Main cleanup function"""
    cleanup = DatabaseCleanup()
    await cleanup.fix_claim_types()

if __name__ == "__main__":
    asyncio.run(main())
