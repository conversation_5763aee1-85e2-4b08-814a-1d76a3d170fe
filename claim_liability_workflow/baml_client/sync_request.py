###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, Union
from typing_extensions import Literal

import baml_py

from . import _baml


class HttpRequest:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaim(
        self,
        emailContent: str,attachments: List[str],ocrResults: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeClaim",
        {
          "emailContent": emailContent,"attachments": attachments,"ocrResults": ocrResults,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ClassifyEmail(
        self,
        subject: str,body: str,hasAttachments: bool,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyEmail",
        {
          "subject": subject,"body": body,"hasAttachments": hasAttachments,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def DetermineHumanAction(
        self,
        claimData: str,currentStatus: str,aiAnalysis: str,availableAgents: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "DetermineHumanAction",
        {
          "claimData": claimData,"currentStatus": currentStatus,"aiAnalysis": aiAnalysis,"availableAgents": availableAgents,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ExtractClaimDetails(
        self,
        emailContent: str,ocrText: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractClaimDetails",
        {
          "emailContent": emailContent,"ocrText": ocrText,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    
    def ProcessAgentResponse(
        self,
        claimId: str,agentResponse: str,claimContext: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ProcessAgentResponse",
        {
          "claimId": claimId,"agentResponse": agentResponse,"claimContext": claimContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        False,
      )
    


class HttpStreamRequest:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager

    
    def AnalyzeClaim(
        self,
        emailContent: str,attachments: List[str],ocrResults: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "AnalyzeClaim",
        {
          "emailContent": emailContent,"attachments": attachments,"ocrResults": ocrResults,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ClassifyEmail(
        self,
        subject: str,body: str,hasAttachments: bool,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ClassifyEmail",
        {
          "subject": subject,"body": body,"hasAttachments": hasAttachments,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def DetermineHumanAction(
        self,
        claimData: str,currentStatus: str,aiAnalysis: str,availableAgents: List[str],
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "DetermineHumanAction",
        {
          "claimData": claimData,"currentStatus": currentStatus,"aiAnalysis": aiAnalysis,"availableAgents": availableAgents,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ExtractClaimDetails(
        self,
        emailContent: str,ocrText: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ExtractClaimDetails",
        {
          "emailContent": emailContent,"ocrText": ocrText,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    
    def ProcessAgentResponse(
        self,
        claimId: str,agentResponse: str,claimContext: str,
        baml_options: _baml.BamlCallOptionsModApi = {},
    ) -> baml_py.HTTPRequest:
      __tb__ = baml_options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = baml_options.get("client_registry", None)
      env = _baml.env_vars_to_dict(baml_options.get("env", {}))

      return self.__runtime.build_request_sync(
        "ProcessAgentResponse",
        {
          "claimId": claimId,"agentResponse": agentResponse,"claimContext": claimContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        env,
        True,
      )
    


__all__ = ["HttpRequest", "HttpStreamRequest"]