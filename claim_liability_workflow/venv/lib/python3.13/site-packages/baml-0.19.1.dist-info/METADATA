Metadata-Version: 2.1
Name: baml
Version: 0.19.1
Summary: 
Author: Boundary
Author-email: <EMAIL>
Requires-Python: >=3.8,<4.0
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Dist: aiohttp (>=3.8.3)
Requires-Dist: anthropic (>=0.14.0)
Requires-Dist: baml-core-ffi (==0.1.7)
Requires-Dist: colorama (>=0.4.6)
Requires-Dist: coloredlogs (>=15.0.1)
Requires-Dist: json5 (>=0.9.10)
Requires-Dist: ollama (>=0.1.8,<0.2.0)
Requires-Dist: openai (>=0.28.1)
Requires-Dist: opentelemetry-api (>=1.15.0)
Requires-Dist: opentelemetry-instrumentation (>=0.34b0)
Requires-Dist: opentelemetry-sdk (>=1.15.0)
Requires-Dist: packaging (>=23.2,<24.0)
Requires-Dist: pydantic (>=2,<3)
Requires-Dist: pytest (>=7.1.3,<8)
Requires-Dist: pytest-asyncio (>=0.21.1,<0.22.0)
Requires-Dist: python-dotenv (>=0.21.0)
Requires-Dist: regex (>=2022.10.31)
Requires-Dist: tenacity (>=8.1.0)
Requires-Dist: typeguard (>=4.0.0)
Requires-Dist: types-regex (>=2023.10.3.0,<2024.0.0.0)
Requires-Dist: types-requests (>=2.28.11)
