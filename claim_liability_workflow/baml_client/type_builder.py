###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import typing
from baml_py.baml_py import FieldType, <PERSON>um<PERSON><PERSON>ue<PERSON><PERSON><PERSON>, En<PERSON><PERSON>uilder, ClassBuilder
from baml_py.type_builder import <PERSON><PERSON><PERSON><PERSON> as _<PERSON><PERSON><PERSON>er, Class<PERSON>roperty<PERSON>uilder, ClassPropertyViewer, <PERSON>um<PERSON><PERSON><PERSON><PERSON>iewer
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME


class TypeBuilder(_TypeBuilder):
    def __init__(self):
        super().__init__(classes=set(
          ["AgentAssignment","AgentDecision","AgentResponse","ClaimAnalysis","ClaimClassification","CoverageAnalysis","CustomerCommunication","DocumentAnalysis","DocumentExtraction","DocumentProcessing","EmailClassification","EscalateClaim","FraudAnalysis","FraudInvestigation","LiabilityAssessment","RequestMoreInformation","TeamNotification","UpdateClaimStatus","WorkflowCompletion",]
        ), enums=set(
          []
        ), runtime=DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME)


    @property
    def AgentAssignment(self) -> "AgentAssignmentAst":
        return AgentAssignmentAst(self)

    @property
    def AgentDecision(self) -> "AgentDecisionAst":
        return AgentDecisionAst(self)

    @property
    def AgentResponse(self) -> "AgentResponseAst":
        return AgentResponseAst(self)

    @property
    def ClaimAnalysis(self) -> "ClaimAnalysisAst":
        return ClaimAnalysisAst(self)

    @property
    def ClaimClassification(self) -> "ClaimClassificationAst":
        return ClaimClassificationAst(self)

    @property
    def CoverageAnalysis(self) -> "CoverageAnalysisAst":
        return CoverageAnalysisAst(self)

    @property
    def CustomerCommunication(self) -> "CustomerCommunicationAst":
        return CustomerCommunicationAst(self)

    @property
    def DocumentAnalysis(self) -> "DocumentAnalysisAst":
        return DocumentAnalysisAst(self)

    @property
    def DocumentExtraction(self) -> "DocumentExtractionAst":
        return DocumentExtractionAst(self)

    @property
    def DocumentProcessing(self) -> "DocumentProcessingAst":
        return DocumentProcessingAst(self)

    @property
    def EmailClassification(self) -> "EmailClassificationAst":
        return EmailClassificationAst(self)

    @property
    def EscalateClaim(self) -> "EscalateClaimAst":
        return EscalateClaimAst(self)

    @property
    def FraudAnalysis(self) -> "FraudAnalysisAst":
        return FraudAnalysisAst(self)

    @property
    def FraudInvestigation(self) -> "FraudInvestigationAst":
        return FraudInvestigationAst(self)

    @property
    def LiabilityAssessment(self) -> "LiabilityAssessmentAst":
        return LiabilityAssessmentAst(self)

    @property
    def RequestMoreInformation(self) -> "RequestMoreInformationAst":
        return RequestMoreInformationAst(self)

    @property
    def TeamNotification(self) -> "TeamNotificationAst":
        return TeamNotificationAst(self)

    @property
    def UpdateClaimStatus(self) -> "UpdateClaimStatusAst":
        return UpdateClaimStatusAst(self)

    @property
    def WorkflowCompletion(self) -> "WorkflowCompletionAst":
        return WorkflowCompletionAst(self)





class AgentAssignmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AgentAssignment")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "agentType",  "priority",  "reason",  "estimatedProcessingTime", ])
        self._props = AgentAssignmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AgentAssignmentProperties":
        return self._props


class AgentAssignmentViewer(AgentAssignmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AgentAssignmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def agentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("agentType"))

    @property
    def priority(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priority"))

    @property
    def reason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reason"))

    @property
    def estimatedProcessingTime(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedProcessingTime"))

    

class AgentDecisionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AgentDecision")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "decision",  "settlementAmount",  "reasoning",  "nextSteps",  "requiresManagerApproval", ])
        self._props = AgentDecisionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AgentDecisionProperties":
        return self._props


class AgentDecisionViewer(AgentDecisionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AgentDecisionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def decision(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decision"))

    @property
    def settlementAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("settlementAmount"))

    @property
    def reasoning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reasoning"))

    @property
    def nextSteps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nextSteps"))

    @property
    def requiresManagerApproval(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresManagerApproval"))

    

class AgentResponseAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("AgentResponse")
        self._properties: typing.Set[str] = set([ "agentId",  "responseType",  "decision",  "reasoning",  "nextActions",  "confidenceLevel", ])
        self._props = AgentResponseProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "AgentResponseProperties":
        return self._props


class AgentResponseViewer(AgentResponseAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class AgentResponseProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def agentId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("agentId"))

    @property
    def responseType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("responseType"))

    @property
    def decision(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("decision"))

    @property
    def reasoning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reasoning"))

    @property
    def nextActions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nextActions"))

    @property
    def confidenceLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceLevel"))

    

class ClaimAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimAnalysis")
        self._properties: typing.Set[str] = set([ "classification",  "fraudAnalysis",  "coverageAnalysis",  "documents",  "liabilityAssessment",  "overallRecommendation",  "reasoning",  "nextSteps",  "priority", ])
        self._props = ClaimAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimAnalysisProperties":
        return self._props


class ClaimAnalysisViewer(ClaimAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def classification(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("classification"))

    @property
    def fraudAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fraudAnalysis"))

    @property
    def coverageAnalysis(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageAnalysis"))

    @property
    def documents(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documents"))

    @property
    def liabilityAssessment(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("liabilityAssessment"))

    @property
    def overallRecommendation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("overallRecommendation"))

    @property
    def reasoning(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("reasoning"))

    @property
    def nextSteps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("nextSteps"))

    @property
    def priority(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priority"))

    

class ClaimClassificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("ClaimClassification")
        self._properties: typing.Set[str] = set([ "claimType",  "severity",  "estimatedValue",  "confidenceScore", ])
        self._props = ClaimClassificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "ClaimClassificationProperties":
        return self._props


class ClaimClassificationViewer(ClaimClassificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class ClaimClassificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def claimType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimType"))

    @property
    def severity(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("severity"))

    @property
    def estimatedValue(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedValue"))

    @property
    def confidenceScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceScore"))

    

class CoverageAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CoverageAnalysis")
        self._properties: typing.Set[str] = set([ "policyValid",  "coverageType",  "deductible",  "coverageLimit",  "exclusions",  "recommendation", ])
        self._props = CoverageAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CoverageAnalysisProperties":
        return self._props


class CoverageAnalysisViewer(CoverageAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CoverageAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def policyValid(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyValid"))

    @property
    def coverageType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageType"))

    @property
    def deductible(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("deductible"))

    @property
    def coverageLimit(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("coverageLimit"))

    @property
    def exclusions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("exclusions"))

    @property
    def recommendation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendation"))

    

class CustomerCommunicationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("CustomerCommunication")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "communicationType",  "message",  "attachments",  "urgency", ])
        self._props = CustomerCommunicationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "CustomerCommunicationProperties":
        return self._props


class CustomerCommunicationViewer(CustomerCommunicationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class CustomerCommunicationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def communicationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("communicationType"))

    @property
    def message(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("message"))

    @property
    def attachments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("attachments"))

    @property
    def urgency(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgency"))

    

class DocumentAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentAnalysis")
        self._properties: typing.Set[str] = set([ "documentType",  "extractedData",  "completeness",  "quality",  "requiredForClaim", ])
        self._props = DocumentAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentAnalysisProperties":
        return self._props


class DocumentAnalysisViewer(DocumentAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def documentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentType"))

    @property
    def extractedData(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("extractedData"))

    @property
    def completeness(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("completeness"))

    @property
    def quality(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("quality"))

    @property
    def requiredForClaim(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiredForClaim"))

    

class DocumentExtractionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentExtraction")
        self._properties: typing.Set[str] = set([ "policyNumber",  "claimAmount",  "incidentDate",  "location",  "partiesInvolved",  "damages",  "witnesses",  "policeReportNumber",  "medicalInformation", ])
        self._props = DocumentExtractionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentExtractionProperties":
        return self._props


class DocumentExtractionViewer(DocumentExtractionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentExtractionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def policyNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policyNumber"))

    @property
    def claimAmount(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimAmount"))

    @property
    def incidentDate(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("incidentDate"))

    @property
    def location(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("location"))

    @property
    def partiesInvolved(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("partiesInvolved"))

    @property
    def damages(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("damages"))

    @property
    def witnesses(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("witnesses"))

    @property
    def policeReportNumber(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("policeReportNumber"))

    @property
    def medicalInformation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("medicalInformation"))

    

class DocumentProcessingAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("DocumentProcessing")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "documentType",  "processingAction",  "priority",  "expectedOutput", ])
        self._props = DocumentProcessingProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "DocumentProcessingProperties":
        return self._props


class DocumentProcessingViewer(DocumentProcessingAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class DocumentProcessingProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def documentType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("documentType"))

    @property
    def processingAction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("processingAction"))

    @property
    def priority(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("priority"))

    @property
    def expectedOutput(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("expectedOutput"))

    

class EmailClassificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EmailClassification")
        self._properties: typing.Set[str] = set([ "isClaim",  "claimType",  "urgency",  "requiresImmediateResponse",  "suggestedResponse", ])
        self._props = EmailClassificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EmailClassificationProperties":
        return self._props


class EmailClassificationViewer(EmailClassificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EmailClassificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def isClaim(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("isClaim"))

    @property
    def claimType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimType"))

    @property
    def urgency(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgency"))

    @property
    def requiresImmediateResponse(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresImmediateResponse"))

    @property
    def suggestedResponse(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("suggestedResponse"))

    

class EscalateClaimAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("EscalateClaim")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "escalationReason",  "escalationLevel",  "urgency",  "additionalContext", ])
        self._props = EscalateClaimProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "EscalateClaimProperties":
        return self._props


class EscalateClaimViewer(EscalateClaimAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class EscalateClaimProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def escalationReason(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("escalationReason"))

    @property
    def escalationLevel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("escalationLevel"))

    @property
    def urgency(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgency"))

    @property
    def additionalContext(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("additionalContext"))

    

class FraudAnalysisAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FraudAnalysis")
        self._properties: typing.Set[str] = set([ "riskScore",  "redFlags",  "fraudIndicators",  "recommendation",  "confidenceScore", ])
        self._props = FraudAnalysisProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FraudAnalysisProperties":
        return self._props


class FraudAnalysisViewer(FraudAnalysisAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FraudAnalysisProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def riskScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("riskScore"))

    @property
    def redFlags(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("redFlags"))

    @property
    def fraudIndicators(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("fraudIndicators"))

    @property
    def recommendation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendation"))

    @property
    def confidenceScore(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("confidenceScore"))

    

class FraudInvestigationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("FraudInvestigation")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "investigationType",  "redFlags",  "investigationSteps",  "estimatedDuration",  "requiresExternalResources", ])
        self._props = FraudInvestigationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "FraudInvestigationProperties":
        return self._props


class FraudInvestigationViewer(FraudInvestigationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class FraudInvestigationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def investigationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationType"))

    @property
    def redFlags(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("redFlags"))

    @property
    def investigationSteps(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("investigationSteps"))

    @property
    def estimatedDuration(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("estimatedDuration"))

    @property
    def requiresExternalResources(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresExternalResources"))

    

class LiabilityAssessmentAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("LiabilityAssessment")
        self._properties: typing.Set[str] = set([ "faultDetermination",  "faultPercentage",  "contributingFactors",  "evidenceStrength",  "recommendation", ])
        self._props = LiabilityAssessmentProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "LiabilityAssessmentProperties":
        return self._props


class LiabilityAssessmentViewer(LiabilityAssessmentAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class LiabilityAssessmentProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def faultDetermination(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultDetermination"))

    @property
    def faultPercentage(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("faultPercentage"))

    @property
    def contributingFactors(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("contributingFactors"))

    @property
    def evidenceStrength(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("evidenceStrength"))

    @property
    def recommendation(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendation"))

    

class RequestMoreInformationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("RequestMoreInformation")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "requestedDocuments",  "questions",  "deadline",  "urgency", ])
        self._props = RequestMoreInformationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "RequestMoreInformationProperties":
        return self._props


class RequestMoreInformationViewer(RequestMoreInformationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class RequestMoreInformationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def requestedDocuments(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requestedDocuments"))

    @property
    def questions(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("questions"))

    @property
    def deadline(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("deadline"))

    @property
    def urgency(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("urgency"))

    

class TeamNotificationAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("TeamNotification")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "notificationType",  "channel",  "message",  "recipients",  "requiresAction", ])
        self._props = TeamNotificationProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "TeamNotificationProperties":
        return self._props


class TeamNotificationViewer(TeamNotificationAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class TeamNotificationProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def notificationType(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("notificationType"))

    @property
    def channel(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("channel"))

    @property
    def message(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("message"))

    @property
    def recipients(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recipients"))

    @property
    def requiresAction(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("requiresAction"))

    

class UpdateClaimStatusAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("UpdateClaimStatus")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "newStatus",  "statusNotes",  "notifyCustomer",  "internalNotes", ])
        self._props = UpdateClaimStatusProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "UpdateClaimStatusProperties":
        return self._props


class UpdateClaimStatusViewer(UpdateClaimStatusAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class UpdateClaimStatusProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def newStatus(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("newStatus"))

    @property
    def statusNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("statusNotes"))

    @property
    def notifyCustomer(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("notifyCustomer"))

    @property
    def internalNotes(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("internalNotes"))

    

class WorkflowCompletionAst:
    def __init__(self, tb: _TypeBuilder):
        _tb = tb._tb # type: ignore (we know how to use this private attribute)
        self._bldr = _tb.class_("WorkflowCompletion")
        self._properties: typing.Set[str] = set([ "intent",  "claimId",  "finalStatus",  "summary",  "lessonsLearned",  "recommendations", ])
        self._props = WorkflowCompletionProperties(self._bldr, self._properties)

    def type(self) -> FieldType:
        return self._bldr.field()

    @property
    def props(self) -> "WorkflowCompletionProperties":
        return self._props


class WorkflowCompletionViewer(WorkflowCompletionAst):
    def __init__(self, tb: _TypeBuilder):
        super().__init__(tb)

    
    def list_properties(self) -> typing.List[typing.Tuple[str, ClassPropertyViewer]]:
        return [(name, ClassPropertyViewer(self._bldr.property(name))) for name in self._properties]



class WorkflowCompletionProperties:
    def __init__(self, bldr: ClassBuilder, properties: typing.Set[str]):
        self.__bldr = bldr
        self.__properties = properties

    

    @property
    def intent(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("intent"))

    @property
    def claimId(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("claimId"))

    @property
    def finalStatus(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("finalStatus"))

    @property
    def summary(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("summary"))

    @property
    def lessonsLearned(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("lessonsLearned"))

    @property
    def recommendations(self) -> ClassPropertyViewer:
        return ClassPropertyViewer(self.__bldr.property("recommendations"))

    




__all__ = ["TypeBuilder"]