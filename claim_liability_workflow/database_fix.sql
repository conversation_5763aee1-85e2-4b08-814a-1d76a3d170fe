-- Claims Liability Workflow - Database Schema Fix
-- Run this in your Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================================================
-- DROP EXISTING TABLES AND TYPES (if they exist)
-- =============================================================================

-- Drop tables in correct order (due to foreign key constraints)
DROP TABLE IF EXISTS claim_timeline CASCADE;
DROP TABLE IF EXISTS claim_documents CASCADE;
DROP TABLE IF EXISTS agent_decisions CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS inquiries CASCADE;
DROP TABLE IF EXISTS claims CASCADE;

-- Drop existing enum types
DROP TYPE IF EXISTS claim_status CASCADE;
DROP TYPE IF EXISTS notification_channel CASCADE;
DROP TYPE IF EXISTS event_type CASCADE;
DROP TYPE IF EXISTS decision_type CASCADE;
DROP TYPE IF EXISTS document_type CASCADE;

-- =============================================================================
-- CREATE ENUM TYPES
-- =============================================================================

-- Claim status enum
CREATE TYPE claim_status AS ENUM (
    'received',
    'documents_processing',
    'ai_analysis',
    'human_review',
    'pending_approval',
    'approved',
    'denied',
    'more_info_required',
    'closed',
    'escalated'
);

-- Notification channel enum
CREATE TYPE notification_channel AS ENUM (
    'email',
    'sms',
    'whatsapp',
    'slack',
    'zendesk'
);

-- Event type enum
CREATE TYPE event_type AS ENUM (
    'claim_created',
    'documents_uploaded',
    'ai_analysis_started',
    'ai_analysis_completed',
    'assigned_to_agent',
    'human_review_started',
    'decision_made',
    'notification_sent',
    'status_updated',
    'escalated',
    'closed'
);

-- Decision type enum
CREATE TYPE decision_type AS ENUM (
    'approve',
    'deny',
    'request_more_info',
    'escalate'
);

-- Document type enum
CREATE TYPE document_type AS ENUM (
    'incident_report',
    'medical_report',
    'police_report',
    'photos',
    'receipts',
    'insurance_policy',
    'other'
);

-- =============================================================================
-- CREATE TABLES
-- =============================================================================

-- Claims table - Main claim records
CREATE TABLE claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_number VARCHAR(50) UNIQUE NOT NULL,
    zendesk_ticket_id BIGINT UNIQUE,
    
    -- User Information
    user_email VARCHAR(255) NOT NULL,
    user_phone VARCHAR(20),
    user_name VARCHAR(255),
    
    -- Claim Details
    subject TEXT NOT NULL,
    description TEXT,
    incident_date DATE,
    reported_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Status and Assignment
    status claim_status DEFAULT 'received',
    priority INTEGER DEFAULT 3, -- 1=urgent, 2=high, 3=normal, 4=low
    assigned_agent_id UUID,
    
    -- Financial Information
    claimed_amount DECIMAL(12,2),
    approved_amount DECIMAL(12,2),
    
    -- AI Analysis
    ai_confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    fraud_risk_score DECIMAL(3,2), -- 0.00 to 1.00
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    CONSTRAINT valid_amounts CHECK (
        claimed_amount >= 0 AND 
        approved_amount >= 0
    ),
    CONSTRAINT valid_scores CHECK (
        ai_confidence_score BETWEEN 0 AND 1 AND
        fraud_risk_score BETWEEN 0 AND 1
    )
);

-- Inquiries table - For general inquiries (non-claims)
CREATE TABLE inquiries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_email VARCHAR(255) NOT NULL,
    from_name VARCHAR(255),
    subject TEXT,
    body TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Claim documents table
CREATE TABLE claim_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    
    -- File Information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    document_type document_type DEFAULT 'other',
    
    -- OCR and Analysis
    ocr_text TEXT,
    ocr_confidence DECIMAL(3,2),
    extracted_data JSONB DEFAULT '{}',
    
    -- Processing Status
    is_processed BOOLEAN DEFAULT false,
    processing_error TEXT,
    
    -- Timestamps
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Claim timeline table - Complete audit trail
CREATE TABLE claim_timeline (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    
    -- Event Information
    event_type event_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Actor Information
    actor_type VARCHAR(50) NOT NULL, -- 'system', 'agent', 'user', 'ai'
    actor_id UUID,
    actor_name VARCHAR(255),
    actor_email VARCHAR(255),
    
    -- Event Data
    event_data JSONB DEFAULT '{}',
    previous_status claim_status,
    new_status claim_status,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Notifications table - Track all notifications sent
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID REFERENCES claims(id) ON DELETE CASCADE,
    
    -- Notification Details
    channel notification_channel NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    template_name VARCHAR(100),
    
    -- Status
    status VARCHAR(50) DEFAULT 'pending', -- pending, sent, delivered, failed
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    -- External IDs
    external_id VARCHAR(255), -- SMS ID, email ID, etc.
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- =============================================================================
-- CREATE INDEXES
-- =============================================================================

-- Claims table indexes
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_claims_user_email ON claims(user_email);
CREATE INDEX idx_claims_created_at ON claims(created_at);
CREATE INDEX idx_claims_claim_number ON claims(claim_number);
CREATE INDEX idx_claims_zendesk_ticket_id ON claims(zendesk_ticket_id);

-- Inquiries table indexes
CREATE INDEX idx_inquiries_from_email ON inquiries(from_email);
CREATE INDEX idx_inquiries_created_at ON inquiries(created_at);

-- Documents table indexes
CREATE INDEX idx_claim_documents_claim_id ON claim_documents(claim_id);
CREATE INDEX idx_claim_documents_uploaded_at ON claim_documents(uploaded_at);

-- Timeline table indexes
CREATE INDEX idx_claim_timeline_claim_id ON claim_timeline(claim_id);
CREATE INDEX idx_claim_timeline_event_type ON claim_timeline(event_type);
CREATE INDEX idx_claim_timeline_created_at ON claim_timeline(created_at);

-- Notifications table indexes
CREATE INDEX idx_notifications_claim_id ON notifications(claim_id);
CREATE INDEX idx_notifications_channel ON notifications(channel);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- =============================================================================
-- CREATE FUNCTIONS AND TRIGGERS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_claims_updated_at 
    BEFORE UPDATE ON claims 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to create timeline entry on claim status change
CREATE OR REPLACE FUNCTION create_status_timeline_entry()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO claim_timeline (
            claim_id,
            event_type,
            title,
            description,
            actor_type,
            previous_status,
            new_status,
            event_data
        ) VALUES (
            NEW.id,
            'status_updated',
            'Claim status changed',
            'Status changed from ' || COALESCE(OLD.status::text, 'null') || ' to ' || NEW.status::text,
            'system',
            OLD.status,
            NEW.status,
            jsonb_build_object(
                'previous_status', OLD.status,
                'new_status', NEW.status,
                'changed_at', NOW()
            )
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to create timeline entry on status change
CREATE TRIGGER create_status_timeline_entry_trigger
    AFTER UPDATE ON claims
    FOR EACH ROW
    EXECUTE FUNCTION create_status_timeline_entry();

-- =============================================================================
-- INSERT SAMPLE DATA (Optional)
-- =============================================================================

-- Insert a sample claim for testing
INSERT INTO claims (
    claim_number,
    user_email,
    user_name,
    subject,
    description,
    status,
    priority
) VALUES (
    'CLAIM-2025-SAMPLE',
    '<EMAIL>',
    'Test User',
    'Sample Claim for Testing',
    'This is a sample claim created during database setup.',
    'received',
    3
);

-- =============================================================================
-- COMPLETION MESSAGE
-- =============================================================================

-- This will show in the results
SELECT 'Database schema created successfully! ✅' as status;
