###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off

file_map = {
    
    "claims_analysis.baml": "class ClaimClassification {\n  claimType \"auto\" | \"property\" | \"liability\" | \"professional\" | \"bodily_injury\" @description(\"The type of insurance claim\")\n  severity \"minor\" | \"major\" | \"catastrophic\" @description(\"Severity level of the claim\")\n  estimatedValue float @description(\"Estimated claim value in CAD\")\n  confidenceScore float @description(\"AI confidence in the classification (0-1)\")\n}\n\nclass FraudAnalysis {\n  riskScore int @description(\"Fraud risk score from 0-100\")\n  redFlags string[] @description(\"List of identified red flags\")\n  fraudIndicators string[] @description(\"Specific fraud indicators found\")\n  recommendation \"approve\" | \"investigate\" | \"deny\" @description(\"AI recommendation based on fraud analysis\")\n  confidenceScore float @description(\"Confidence in fraud assessment (0-1)\")\n}\n\nclass CoverageAnalysis {\n  policyValid bool @description(\"Whether the policy is valid and active\")\n  coverageType string @description(\"Type of coverage applicable\")\n  deductible float @description(\"Applicable deductible amount\")\n  coverageLimit float @description(\"Maximum coverage limit\")\n  exclusions string[] @description(\"List of coverage exclusions\")\n  recommendation \"covered\" | \"partially_covered\" | \"not_covered\" @description(\"Coverage recommendation\")\n}\n\nclass DocumentAnalysis {\n  documentType \"police_report\" | \"medical_record\" | \"receipt\" | \"photo\" | \"insurance_certificate\" | \"other\" @description(\"Type of document\")\n  extractedData map<string, string> @description(\"Key-value pairs of extracted data\")\n  completeness float @description(\"Document completeness score (0-1)\")\n  quality float @description(\"Document quality score (0-1)\")\n  requiredForClaim bool @description(\"Whether this document is required for the claim\")\n}\n\nclass LiabilityAssessment {\n  faultDetermination \"clear_liability\" | \"shared_liability\" | \"no_liability\" | \"unclear\" @description(\"Liability determination\")\n  faultPercentage float @description(\"Percentage of fault assigned (0-100)\")\n  contributingFactors string[] @description(\"Factors contributing to the incident\")\n  evidenceStrength \"strong\" | \"moderate\" | \"weak\" @description(\"Strength of evidence\")\n  recommendation string @description(\"AI recommendation for liability\")\n}\n\nclass ClaimAnalysis {\n  classification ClaimClassification\n  fraudAnalysis FraudAnalysis\n  coverageAnalysis CoverageAnalysis\n  documents DocumentAnalysis[]\n  liabilityAssessment LiabilityAssessment\n  overallRecommendation \"approve\" | \"deny\" | \"investigate\" | \"request_more_info\" @description(\"Overall AI recommendation\")\n  reasoning string @description(\"Detailed reasoning for the recommendation\")\n  nextSteps string[] @description(\"Recommended next steps\")\n  priority \"low\" | \"medium\" | \"high\" | \"urgent\" @description(\"Claim priority level\")\n}\n\nfunction AnalyzeClaim(\n  emailContent: string,\n  attachments: string[],\n  ocrResults: string[]\n) -> ClaimAnalysis {\n  client OpenAIGPT4O\n  \n  prompt #\"\n    You are an expert insurance claims analyst specializing in Canadian liability claims.\n    \n    Analyze the following claim information and provide a comprehensive assessment:\n    \n    Email Content:\n    {{ emailContent }}\n    \n    Attachments:\n    {{ attachments }}\n    \n    OCR Results:\n    {{ ocrResults }}\n    \n    Please provide a detailed analysis including:\n    1. Claim classification and severity assessment\n    2. Fraud risk analysis with specific indicators\n    3. Coverage analysis and policy validation\n    4. Document analysis and completeness assessment\n    5. Liability assessment and fault determination\n    6. Overall recommendation with reasoning\n    \n    Focus on Canadian insurance regulations and liability laws.\n    Be thorough in identifying potential fraud indicators and coverage issues.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\nclass EmailClassification {\n  isClaim bool @description(\"Whether this email contains a claim\")\n  claimType \"auto\" | \"property\" | \"liability\" | \"professional\" | \"bodily_injury\" | \"general_inquiry\" | \"other\" @description(\"Type of claim or inquiry\")\n  urgency \"low\" | \"medium\" | \"high\" | \"urgent\" @description(\"Urgency level\")\n  requiresImmediateResponse bool @description(\"Whether immediate response is needed\")\n  suggestedResponse string @description(\"Suggested response template\")\n}\n\nfunction ClassifyEmail(\n  subject: string,\n  body: string,\n  hasAttachments: bool\n) -> EmailClassification {\n  client OpenAIGPT4O\n  \n  prompt #\"\n    You are an email classification system for an insurance claims processing company.\n    \n    Classify the following email to determine if it's a claim and what type:\n    \n    Subject: {{ subject }}\n    Body: {{ body }}\n    Has Attachments: {{ hasAttachments }}\n    \n    Determine:\n    1. Is this a claim or general inquiry?\n    2. What type of claim (if applicable)?\n    3. Urgency level\n    4. Whether immediate response is needed\n    5. Suggested response template\n    \n    Focus on Canadian insurance terminology and claim patterns.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\nclass DocumentExtraction {\n  policyNumber string? @description(\"Extracted policy number\")\n  claimAmount float? @description(\"Claim amount mentioned\")\n  incidentDate string? @description(\"Date of incident\")\n  location string? @description(\"Location of incident\")\n  partiesInvolved string[] @description(\"Names of parties involved\")\n  damages string[] @description(\"Types of damages mentioned\")\n  witnesses string[] @description(\"Witness information\")\n  policeReportNumber string? @description(\"Police report number if mentioned\")\n  medicalInformation string[] @description(\"Medical details if applicable\")\n}\n\nfunction ExtractClaimDetails(\n  emailContent: string,\n  ocrText: string\n) -> DocumentExtraction {\n  client OpenAIGPT4O\n  \n  prompt #\"\n    Extract key claim details from the provided email content and OCR text.\n    \n    Email Content:\n    {{ emailContent }}\n    \n    OCR Text:\n    {{ ocrText }}\n    \n    Extract the following information:\n    - Policy number\n    - Claim amount\n    - Incident date\n    - Location\n    - Parties involved\n    - Types of damages\n    - Witness information\n    - Police report number\n    - Medical information (if applicable)\n    \n    Be thorough and extract all relevant information for claims processing.\n    \n    {{ ctx.output_format }}\n  \"#\n} ",
    "clients.baml": "client<llm> OpenAI {\n    provider \"openai\"\n    options {\n        model \"gpt-4o\"\n        api_key env.OPENAI_API_KEY\n    }\n}\n\nclient<llm> OpenAIGPT4O {\n    provider \"openai\"\n    options {\n        model \"gpt-4o\"\n        api_key env.OPENAI_API_KEY\n    }\n}\n",
    "generator.baml": "generator target {\n    output_type \"python/pydantic\"\n    \n    // Where the generated code will be saved (relative to baml_src/)\n    output_dir \"../\"\n    \n    // What interface you prefer to use for the generated code (sync/async)\n    // Both are generated regardless of the choice, just modifies what is exported\n    // at the top level\n    default_client_mode \"async\"\n    \n    // Version of runtime to generate code for (should match installed baml-py version)\n    version \"0.90.2\"\n}\n",
    "human_tools.baml": "class AgentAssignment {\n  intent \"assign_agent\"\n  claimId string @description(\"Unique claim identifier\")\n  agentType \"claims_adjuster\" | \"senior_adjuster\" | \"fraud_investigator\" | \"manager\" | \"legal_counsel\" @description(\"Type of agent to assign\")\n  priority \"low\" | \"medium\" | \"high\" | \"urgent\" @description(\"Assignment priority\")\n  reason string @description(\"Reason for assignment\")\n  estimatedProcessingTime string @description(\"Estimated time for processing\")\n}\n\nclass AgentDecision {\n  intent \"agent_decision\"\n  claimId string @description(\"Unique claim identifier\")\n  decision \"approve\" | \"deny\" | \"investigate\" | \"request_more_info\" | \"escalate\" @description(\"Agent's decision\")\n  settlementAmount float? @description(\"Settlement amount if approved\")\n  reasoning string @description(\"Detailed reasoning for decision\")\n  nextSteps string[] @description(\"Recommended next steps\")\n  requiresManagerApproval bool @description(\"Whether manager approval is required\")\n}\n\nclass RequestMoreInformation {\n  intent \"request_more_info\"\n  claimId string @description(\"Unique claim identifier\")\n  requestedDocuments string[] @description(\"List of documents needed\")\n  questions string[] @description(\"Specific questions for the claimant\")\n  deadline string @description(\"Deadline for providing information\")\n  urgency \"low\" | \"medium\" | \"high\" | \"urgent\" @description(\"Urgency of the request\")\n}\n\nclass EscalateClaim {\n  intent \"escalate_claim\"\n  claimId string @description(\"Unique claim identifier\")\n  escalationReason string @description(\"Reason for escalation\")\n  escalationLevel \"manager\" | \"director\" | \"legal\" | \"fraud_team\" @description(\"Escalation level\")\n  urgency \"high\" | \"urgent\" @description(\"Escalation urgency\")\n  additionalContext string @description(\"Additional context for escalation\")\n}\n\nclass UpdateClaimStatus {\n  intent \"update_status\"\n  claimId string @description(\"Unique claim identifier\")\n  newStatus \"received\" | \"under_review\" | \"pending_documents\" | \"approved\" | \"denied\" | \"investigation\" | \"closed\" @description(\"New claim status\")\n  statusNotes string @description(\"Notes about the status change\")\n  notifyCustomer bool @description(\"Whether to notify the customer\")\n  internalNotes string? @description(\"Internal notes not shared with customer\")\n}\n\nclass CustomerCommunication {\n  intent \"customer_communication\"\n  claimId string @description(\"Unique claim identifier\")\n  communicationType \"acknowledgment\" | \"status_update\" | \"decision_notification\" | \"document_request\" @description(\"Type of communication\")\n  message string @description(\"Message content\")\n  attachments string[] @description(\"Attachments to include\")\n  urgency \"low\" | \"medium\" | \"high\" | \"urgent\" @description(\"Communication urgency\")\n}\n\nclass TeamNotification {\n  intent \"team_notification\"\n  claimId string @description(\"Unique claim identifier\")\n  notificationType \"new_claim\" | \"agent_assigned\" | \"decision_made\" | \"escalation\" | \"urgent_alert\" @description(\"Type of notification\")\n  channel \"slack\" | \"email\" | \"both\" @description(\"Notification channel\")\n  message string @description(\"Notification message\")\n  recipients string[] @description(\"List of recipients\")\n  requiresAction bool @description(\"Whether action is required\")\n}\n\nclass DocumentProcessing {\n  intent \"process_documents\"\n  claimId string @description(\"Unique claim identifier\")\n  documentType \"police_report\" | \"medical_record\" | \"receipt\" | \"photo\" | \"insurance_certificate\" | \"other\" @description(\"Type of document\")\n  processingAction \"ocr\" | \"classify\" | \"validate\" | \"extract_data\" @description(\"Processing action needed\")\n  priority \"low\" | \"medium\" | \"high\" | \"urgent\" @description(\"Processing priority\")\n  expectedOutput string @description(\"Expected output from processing\")\n}\n\nclass FraudInvestigation {\n  intent \"fraud_investigation\"\n  claimId string @description(\"Unique claim identifier\")\n  investigationType \"preliminary\" | \"detailed\" | \"surveillance\" | \"background_check\" @description(\"Type of investigation\")\n  redFlags string[] @description(\"Red flags identified\")\n  investigationSteps string[] @description(\"Steps for investigation\")\n  estimatedDuration string @description(\"Estimated investigation duration\")\n  requiresExternalResources bool @description(\"Whether external resources are needed\")\n}\n\nclass WorkflowCompletion {\n  intent \"workflow_complete\"\n  claimId string @description(\"Unique claim identifier\")\n  finalStatus \"approved\" | \"denied\" | \"closed\" | \"referred\" @description(\"Final claim status\")\n  summary string @description(\"Summary of the claim processing\")\n  lessonsLearned string[] @description(\"Lessons learned from this claim\")\n  recommendations string[] @description(\"Recommendations for similar claims\")\n}\n\ntype HumanTools = AgentAssignment | AgentDecision | RequestMoreInformation | EscalateClaim | UpdateClaimStatus | CustomerCommunication | TeamNotification | DocumentProcessing | FraudInvestigation | WorkflowCompletion\n\nfunction DetermineHumanAction(\n  claimData: string,\n  currentStatus: string,\n  aiAnalysis: string,\n  availableAgents: string[]\n) -> HumanTools {\n  client OpenAIGPT4O\n  \n  prompt #\"\n    You are a workflow orchestrator for an insurance claims processing system.\n    \n    Based on the current claim data, status, AI analysis, and available agents, determine the next human action needed.\n    \n    Claim Data:\n    {{ claimData }}\n    \n    Current Status:\n    {{ currentStatus }}\n    \n    AI Analysis:\n    {{ aiAnalysis }}\n    \n    Available Agents:\n    {{ availableAgents }}\n    \n    Determine the most appropriate human action from the following options:\n    1. assign_agent - Assign a specific agent to handle the claim\n    2. agent_decision - Make a decision on the claim\n    3. request_more_info - Request additional information from the claimant\n    4. escalate_claim - Escalate to a higher level\n    5. update_status - Update the claim status\n    6. customer_communication - Send communication to the customer\n    7. team_notification - Notify the team\n    8. process_documents - Process additional documents\n    9. fraud_investigation - Initiate fraud investigation\n    10. workflow_complete - Complete the workflow\n    \n    Consider the urgency, complexity, and requirements of the claim when making your decision.\n    \n    {{ ctx.output_format }}\n  \"#\n}\n\nclass AgentResponse {\n  agentId string @description(\"ID of the responding agent\")\n  responseType \"approval\" | \"denial\" | \"request_info\" | \"escalation\" | \"status_update\" @description(\"Type of response\")\n  decision string @description(\"Agent's decision or response\")\n  reasoning string @description(\"Reasoning for the decision\")\n  nextActions string[] @description(\"Recommended next actions\")\n  confidenceLevel \"low\" | \"medium\" | \"high\" @description(\"Agent's confidence in the decision\")\n}\n\nfunction ProcessAgentResponse(\n  claimId: string,\n  agentResponse: string,\n  claimContext: string\n) -> AgentResponse {\n  client OpenAIGPT4O\n  \n  prompt #\"\n    Process an agent's response to a claim and structure it appropriately.\n    \n    Claim ID: {{ claimId }}\n    Agent Response: {{ agentResponse }}\n    Claim Context: {{ claimContext }}\n    \n    Extract and structure the agent's response including:\n    - Response type (approval, denial, request for info, etc.)\n    - Decision made\n    - Reasoning provided\n    - Recommended next actions\n    - Confidence level in the decision\n    \n    {{ ctx.output_format }}\n  \"#\n} ",
}

def get_baml_files():
    return file_map