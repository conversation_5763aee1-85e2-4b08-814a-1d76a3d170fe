# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Optional
from typing_extensions import Literal

from ..._models import BaseModel

__all__ = ["ResponseFunctionToolCallOutputItem"]


class ResponseFunctionToolCallOutputItem(BaseModel):
    id: str
    """The unique ID of the function call tool output."""

    call_id: str
    """The unique ID of the function tool call generated by the model."""

    output: str
    """A JSON string of the output of the function tool call."""

    type: Literal["function_call_output"]
    """The type of the function tool call output. Always `function_call_output`."""

    status: Optional[Literal["in_progress", "completed", "incomplete"]] = None
    """The status of the item.

    One of `in_progress`, `completed`, or `incomplete`. Populated when items are
    returned via API.
    """
