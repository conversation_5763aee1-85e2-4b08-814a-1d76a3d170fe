#!/usr/bin/env python3
"""
Database Fix Script - Run inside Docker container
Fixes validation errors by updating old claim types to match new BAML schema
"""

import asyncio
import json
import logging
import sys
import os

# Add the src directory to the path
sys.path.append('/app/src')

from database_manager import DatabaseManager
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Claim type mapping from old to new BAML schema
CLAIM_TYPE_MAPPING = {
    'auto_liability': 'liability',
    'general': 'general_inquiry',
    'auto': 'auto',
    'property': 'property',
    'professional': 'professional',
    'bodily_injury': 'bodily_injury',
    'other': 'other'
}

async def fix_database():
    """Fix invalid claim types in database"""
    logger.info("Starting database cleanup...")
    
    # Initialize database manager
    config = Config()
    config_dict = config.to_dict()
    db_manager = DatabaseManager(config_dict)
    await db_manager.initialize()
    
    try:
        # Get all claims
        result = db_manager.client.table('claims').select('*').execute()
        claims = result.data
        
        logger.info(f"Found {len(claims)} claims to process")
        
        updated_count = 0
        error_count = 0
        
        for claim in claims:
            try:
                # Debug: print claim keys to see what's available
                logger.info(f"Claim keys: {list(claim.keys())}")

                # Try different possible ID fields
                claim_id = claim.get('claim_id') or claim.get('id') or claim.get('claim_number')
                if not claim_id:
                    logger.error(f"No valid ID field found in claim: {claim}")
                    error_count += 1
                    continue
                
                # Parse classification JSON
                classification_str = claim.get('classification', '{}')
                if isinstance(classification_str, str):
                    classification = json.loads(classification_str)
                else:
                    classification = classification_str
                
                # Get current claim type
                current_claim_type = classification.get('claimType', 'other')
                
                # Check if claim type needs updating
                if current_claim_type in CLAIM_TYPE_MAPPING:
                    new_claim_type = CLAIM_TYPE_MAPPING[current_claim_type]
                    
                    if new_claim_type != current_claim_type:
                        logger.info(f"Updating claim {claim_id}: '{current_claim_type}' -> '{new_claim_type}'")
                        
                        # Update classification
                        classification['claimType'] = new_claim_type
                        
                        # Update in database - use the correct ID field
                        id_field = 'claim_id' if 'claim_id' in claim else ('id' if 'id' in claim else 'claim_number')
                        update_result = db_manager.client.table('claims').update({
                            'classification': json.dumps(classification)
                        }).eq(id_field, claim_id).execute()
                        
                        if update_result.data:
                            updated_count += 1
                            logger.info(f"Successfully updated claim {claim_id}")
                        else:
                            error_count += 1
                            logger.error(f"Failed to update claim {claim_id}")
                    else:
                        logger.info(f"Claim {claim_id} already has valid claim type: {current_claim_type}")
                else:
                    # Unknown claim type, set to 'other'
                    logger.warning(f"Unknown claim type '{current_claim_type}' in claim {claim_id}, setting to 'other'")
                    classification['claimType'] = 'other'
                    
                    id_field = 'claim_id' if 'claim_id' in claim else ('id' if 'id' in claim else 'claim_number')
                    update_result = db_manager.client.table('claims').update({
                        'classification': json.dumps(classification)
                    }).eq(id_field, claim_id).execute()
                    
                    if update_result.data:
                        updated_count += 1
                        logger.info(f"Successfully updated claim {claim_id} to 'other'")
                    else:
                        error_count += 1
                        logger.error(f"Failed to update claim {claim_id}")
                        
            except Exception as e:
                logger.error(f"Error processing claim {claim.get('claim_id', 'unknown')}: {e}")
                error_count += 1
        
        logger.info(f"Database cleanup completed:")
        logger.info(f"  - Updated: {updated_count} claims")
        logger.info(f"  - Errors: {error_count} claims")
        logger.info(f"  - Total processed: {len(claims)} claims")
        
        return updated_count, error_count
        
    except Exception as e:
        logger.error(f"Error during database cleanup: {e}")
        return 0, 1

if __name__ == "__main__":
    asyncio.run(fix_database())
