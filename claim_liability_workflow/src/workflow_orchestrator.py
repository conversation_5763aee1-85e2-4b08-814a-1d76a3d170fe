"""
Claims Liability Workflow Orchestrator
Coordinates the complete email-to-resolution process for Canadian liability claims
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import uuid

from baml_client.async_client import b
from baml_client.types import (
    ClaimAnalysis, EmailClassification, DocumentExtraction,
    AgentAssignment, AgentDecision, AgentResponse, RequestMoreInformation,
    EscalateClaim, UpdateClaimStatus, CustomerCommunication, TeamNotification,
    DocumentProcessing, FraudInvestigation, WorkflowCompletion
)
from typing import Union

from .email_monitor import EmailMonitor
from .document_processor import DocumentProcessor
from .zendesk_integration import ZendeskIntegration
from .slack_integration import SlackIntegration
from .human_layer_integration import HumanLayerIntegration
from .database_manager import DatabaseManager
from .notification_service import NotificationService

logger = logging.getLogger(__name__)

@dataclass
class ClaimData:
    """Data structure for claim information"""
    claim_id: str
    email_data: Dict[str, Any]
    classification: EmailClassification
    extracted_details: DocumentExtraction
    ai_analysis: ClaimAnalysis
    status: str
    created_at: datetime
    updated_at: datetime
    zendesk_ticket_id: Optional[str] = None
    assigned_agent: Optional[str] = None
    decision: Optional[Dict[str, Any]] = None

class ClaimsWorkflowOrchestrator:
    """
    Main orchestrator for the claims liability workflow
    Handles the complete email-to-resolution process
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_running = False
        
        # Initialize services
        self.email_monitor = EmailMonitor(config)
        self.document_processor = DocumentProcessor(config)
        self.zendesk_integration = ZendeskIntegration(config)
        self.slack_integration = SlackIntegration(config)
        self.human_layer = HumanLayerIntegration(config)
        self.database = DatabaseManager(config)
        self.notification_service = NotificationService(config)
        
        # Active claims tracking
        self.active_claims: Dict[str, ClaimData] = {}
        
        logger.info("Claims Workflow Orchestrator initialized")
    
    async def start(self):
        """Start the workflow orchestrator"""
        try:
            self.is_running = True
            logger.info("Starting Claims Workflow Orchestrator...")
            
            # Initialize database
            await self.database.initialize()
            
            # Start email monitoring
            await self.email_monitor.start_monitoring(self.process_new_email)
            
            # Start background tasks
            asyncio.create_task(self._monitor_active_claims())
            asyncio.create_task(self._process_pending_claims())
            
            logger.info("Claims Workflow Orchestrator started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start orchestrator: {e}")
            raise
    
    async def stop(self):
        """Stop the workflow orchestrator"""
        self.is_running = False
        await self.email_monitor.stop_monitoring()
        logger.info("Claims Workflow Orchestrator stopped")
    
    async def process_new_email(self, email_data: Dict[str, Any]):
        """
        Process a new incoming email
        This is the entry point for the workflow
        """
        try:
            claim_id = f"CLAIM-{datetime.now().year}-{str(uuid.uuid4())[:8].upper()}"
            logger.info(f"Processing new email for claim {claim_id}")
            
            # Step 1: Classify the email
            classification = await self._classify_email(email_data)
            
            if not classification.isClaim:
                # Handle general inquiry
                await self._handle_general_inquiry(email_data, classification)
                return
            
            # Step 2: Create claim record
            claim_data = ClaimData(
                claim_id=claim_id,
                email_data=email_data,
                classification=classification,
                extracted_details=DocumentExtraction(
                    policyNumber="",
                    claimAmount=0.0,
                    incidentDate="",
                    location="",
                    partiesInvolved=[],
                    damages="",
                    witnesses=[],
                    policeReportNumber="",
                    medicalInformation=""
                ),
                ai_analysis=ClaimAnalysis(),
                status="received",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # Store in database
            await self.database.create_claim(claim_data)
            self.active_claims[claim_id] = claim_data
            
            # Step 3: Send immediate acknowledgment
            await self._send_acknowledgment(claim_data)
            
            # Step 4: Process documents if attachments exist
            if email_data.get('attachments'):
                await self._process_documents(claim_data)
            
            # Step 5: Extract claim details
            await self._extract_claim_details(claim_data)
            
            # Step 6: Perform AI analysis
            await self._perform_ai_analysis(claim_data)
            
            # Step 7: Create Zendesk ticket
            await self._create_zendesk_ticket(claim_data)
            
            # Step 8: Determine next human action
            await self._determine_human_action(claim_data)
            
            logger.info(f"Claim {claim_id} processed successfully")
            
        except Exception as e:
            logger.error(f"Error processing new email: {e}")
            # Send error notification
            await self._send_error_notification(email_data, str(e))
    
    async def _classify_email(self, email_data: Dict[str, Any]) -> EmailClassification:
        """Classify incoming email"""
        try:
            classification = await b.ClassifyEmail(
                subject=email_data.get('subject', ''),
                body=email_data.get('body', ''),
                hasAttachments=bool(email_data.get('attachments'))
            )
            logger.info(f"Email classified as: {classification.claimType}")
            return classification
        except Exception as e:
            logger.error(f"Error classifying email: {e}")
            # Default to general inquiry on error
            return EmailClassification(
                isClaim=False,
                claimType="general_inquiry",
                urgency="low",
                requiresImmediateResponse=False,
                suggestedResponse="Thank you for your inquiry. We will respond shortly."
            )
    
    async def _handle_general_inquiry(self, email_data: Dict[str, Any], classification: EmailClassification):
        """Handle general inquiries (non-claims)"""
        try:
            # Send appropriate response
            await self.notification_service.send_email(
                to_email=email_data.get('from_email'),
                subject="Re: " + email_data.get('subject', ''),
                message=classification.suggestedResponse
            )
            
            # Log the inquiry
            await self.database.log_inquiry(email_data, classification)
            
            logger.info("General inquiry handled successfully")
            
        except Exception as e:
            logger.error(f"Error handling general inquiry: {e}")
    
    async def _send_acknowledgment(self, claim_data: ClaimData):
        """Send immediate acknowledgment to customer"""
        try:
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

Thank you for submitting your claim. We have received your claim and assigned it the reference number: {claim_data.claim_id}

Your claim is now being processed and you will receive updates at each step of the process.

What happens next:
1. We will review your claim and documents
2. An expert will be assigned to your case
3. You will receive a decision within 4 hours
4. We will keep you informed throughout the process

You can track your claim status using this link: {self.config.get('tracking_url', 'https://claims.rozie.ai')}/{claim_data.claim_id}

If you have any questions, please reply to this email or call our claims hotline.

Best regards,
Claims Processing Team
            """
            
            await self.notification_service.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=f"Claim Received - {claim_data.claim_id}",
                message=message
            )
            
            # Update claim status
            claim_data.status = "documents_processing"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            logger.info(f"Acknowledgment sent for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error sending acknowledgment: {e}")
    
    async def _process_documents(self, claim_data: ClaimData):
        """Process attached documents using OCR"""
        try:
            logger.info(f"DEBUG: Starting document processing for claim {claim_data.claim_id}")

            attachments = claim_data.email_data.get('attachments', [])
            logger.info(f"DEBUG: Found {len(attachments)} attachments to process")

            if not attachments:
                logger.warning(f"DEBUG: No attachments found for claim {claim_data.claim_id}")
                return

            # Process each attachment
            ocr_results = []
            for i, attachment in enumerate(attachments):
                logger.info(f"DEBUG: Processing attachment {i+1}/{len(attachments)}")
                logger.info(f"DEBUG: Attachment details: {attachment}")

                try:
                    logger.info(f"DEBUG: Calling document_processor.process_document for {attachment.get('filename', 'unknown')}")
                    ocr_result = await self.document_processor.process_document(attachment)
                    logger.info(f"DEBUG: OCR result received: {len(ocr_result) if ocr_result else 0} characters")

                    if ocr_result:
                        ocr_results.append(ocr_result)
                        logger.info(f"DEBUG: Added OCR result to list. Total results: {len(ocr_results)}")
                    else:
                        logger.warning(f"DEBUG: Empty OCR result for {attachment.get('filename', 'unknown')}")

                except Exception as e:
                    logger.error(f"DEBUG: Error processing attachment {attachment.get('filename', 'unknown')}: {e}")
                    logger.error(f"DEBUG: Exception type: {type(e).__name__}")
                    import traceback
                    logger.error(f"DEBUG: Traceback: {traceback.format_exc()}")

            logger.info(f"DEBUG: Total OCR results collected: {len(ocr_results)}")

            # Store OCR results
            try:
                logger.info(f"DEBUG: Storing OCR results in database")
                await self.database.store_ocr_results(claim_data.claim_id, ocr_results)
                logger.info(f"DEBUG: OCR results stored successfully")
            except Exception as e:
                logger.error(f"DEBUG: Error storing OCR results: {e}")

            # Update claim with OCR results
            try:
                logger.info(f"DEBUG: Updating claim with OCR results")
                claim_data.email_data['ocr_results'] = ocr_results
                claim_data.updated_at = datetime.now()
                await self.database.update_claim(claim_data)
                logger.info(f"DEBUG: Claim updated with OCR results")
            except Exception as e:
                logger.error(f"DEBUG: Error updating claim: {e}")

            logger.info(f"DEBUG: Document processing completed for claim {claim_data.claim_id}")

        except Exception as e:
            logger.error(f"DEBUG: Error in _process_documents: {e}")
            import traceback
            logger.error(f"DEBUG: Traceback: {traceback.format_exc()}")
    
    async def _extract_claim_details(self, claim_data: ClaimData):
        """Extract structured claim details from email and OCR"""
        try:
            email_content = claim_data.email_data.get('body', '')
            ocr_text = '\n'.join(claim_data.email_data.get('ocr_results', []))
            
            extracted_details = await b.ExtractClaimDetails(
                emailContent=email_content,
                ocrText=ocr_text
            )
            
            claim_data.extracted_details = extracted_details
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            logger.info(f"Claim details extracted for {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error extracting claim details: {e}")
    
    async def _perform_ai_analysis(self, claim_data: ClaimData):
        """Perform comprehensive AI analysis of the claim"""
        try:
            logger.info(f"Performing AI analysis for claim {claim_data.claim_id}")
            
            email_content = claim_data.email_data.get('body', '')
            attachments = [att.get('filename', '') for att in claim_data.email_data.get('attachments', [])]
            ocr_results = claim_data.email_data.get('ocr_results', [])
            
            ai_analysis = await b.AnalyzeClaim(
                emailContent=email_content,
                attachments=attachments,
                ocrResults=ocr_results
            )
            
            claim_data.ai_analysis = ai_analysis
            claim_data.status = "ai_analysis"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            logger.info(f"AI analysis completed for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error performing AI analysis: {e}")
    
    async def _create_zendesk_ticket(self, claim_data: ClaimData):
        """Create Zendesk ticket for the claim"""
        try:
            logger.info(f"Creating Zendesk ticket for claim {claim_data.claim_id}")
            
            ticket_data = {
                'subject': f"[{claim_data.claim_id}] {claim_data.email_data.get('subject', 'New Claim')}",
                'description': self._format_zendesk_description(claim_data),
                'priority': self._map_priority(claim_data.ai_analysis.priority),
                'tags': ['aria', 'claims', 'automated', claim_data.classification.claimType],
                'requester_email': claim_data.email_data.get('from_email'),
                'attachments': claim_data.email_data.get('attachments', [])
            }
            
            ticket_id = await self.zendesk_integration.create_ticket(ticket_data)
            claim_data.zendesk_ticket_id = ticket_id
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            logger.info(f"Zendesk ticket {ticket_id} created for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error creating Zendesk ticket: {e}")
    
    async def _determine_human_action(self, claim_data: ClaimData):
        """Determine the next human action needed"""
        try:
            logger.info(f"Determining human action for claim {claim_data.claim_id}")
            
            # Get available agents
            available_agents = await self.database.get_available_agents()
            
            human_action = await b.DetermineHumanAction(
                claimData=str(claim_data),
                currentStatus=claim_data.status,
                aiAnalysis=str(claim_data.ai_analysis),
                availableAgents=available_agents
            )
            
            # Execute the human action
            await self._execute_human_action(claim_data, human_action)
            
        except Exception as e:
            logger.error(f"Error determining human action: {e}")
    
    async def _execute_human_action(self, claim_data: ClaimData, action: Union[AgentAssignment, AgentDecision, RequestMoreInformation, EscalateClaim, UpdateClaimStatus, CustomerCommunication, TeamNotification, DocumentProcessing, FraudInvestigation, WorkflowCompletion]):
        """Execute the determined human action"""
        try:
            if action.intent == "assign_agent":
                await self._assign_agent(claim_data, action)
            elif action.intent == "team_notification":
                await self._send_team_notification(claim_data, action)
            elif action.intent == "customer_communication":
                await self._send_customer_communication(claim_data, action)
            elif action.intent == "update_status":
                await self._update_claim_status(claim_data, action)
            else:
                logger.warning(f"Unhandled human action: {action.intent}")
                
        except Exception as e:
            logger.error(f"Error executing human action: {e}")
    
    async def _assign_agent(self, claim_data: ClaimData, action):
        """Assign an agent to the claim using HumanLayer"""
        try:
            # Use HumanLayer to assign agent
            agent_assignment = await self.human_layer.assign_agent(
                claim_id=claim_data.claim_id,
                agent_type=action.agentType,
                priority=action.priority,
                reason=action.reason
            )
            
            claim_data.assigned_agent = agent_assignment['agent_id']
            claim_data.status = "assigned"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            # Send Slack notification
            await self.slack_integration.send_claim_assignment(
                claim_data, agent_assignment
            )
            
            logger.info(f"Agent {agent_assignment['agent_id']} assigned to claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error assigning agent: {e}")
    
    async def _send_team_notification(self, claim_data: ClaimData, action):
        """Send team notification via Slack and email"""
        try:
            if action.channel in ["slack", "both"]:
                await self.slack_integration.send_notification(
                    channel=self.config['slack_claims_channel'],
                    message=action.message,
                    requires_action=action.requiresAction
                )
            
            if action.channel in ["email", "both"]:
                for recipient in action.recipients:
                    await self.notification_service.send_email(
                        to_email=recipient,
                        subject=f"Claim Update - {claim_data.claim_id}",
                        message=action.message
                    )
            
            logger.info(f"Team notification sent for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error sending team notification: {e}")
    
    async def _send_customer_communication(self, claim_data: ClaimData, action):
        """Send communication to customer"""
        try:
            await self.notification_service.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=f"Claim Update - {claim_data.claim_id}",
                message=action.message
            )
            
            # Update Zendesk ticket
            if claim_data.zendesk_ticket_id:
                await self.zendesk_integration.add_comment(
                    ticket_id=claim_data.zendesk_ticket_id,
                    comment=f"Customer communication sent: {action.communicationType}"
                )
            
            logger.info(f"Customer communication sent for claim {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error sending customer communication: {e}")
    
    async def _update_claim_status(self, claim_data: ClaimData, action):
        """Update claim status"""
        try:
            claim_data.status = action.newStatus
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
            # Update Zendesk ticket
            if claim_data.zendesk_ticket_id:
                await self.zendesk_integration.update_ticket_status(
                    ticket_id=claim_data.zendesk_ticket_id,
                    status=action.newStatus,
                    comment=action.statusNotes
                )
            
            # Notify customer if required
            if action.notifyCustomer:
                await self._send_status_update_to_customer(claim_data, action)
            
            logger.info(f"Claim status updated to {action.newStatus} for {claim_data.claim_id}")
            
        except Exception as e:
            logger.error(f"Error updating claim status: {e}")
    
    async def _send_status_update_to_customer(self, claim_data: ClaimData, action):
        """Send status update to customer"""
        try:
            status_messages = {
                "under_review": "Your claim is currently under review by our expert team.",
                "pending_documents": "We need additional documents to process your claim.",
                "approved": "Your claim has been approved!",
                "denied": "Your claim has been denied.",
                "investigation": "Your claim is under investigation.",
                "closed": "Your claim has been closed."
            }
            
            message = f"""
Dear {claim_data.email_data.get('from_name', 'Valued Customer')},

{status_messages.get(action.newStatus, "Your claim status has been updated.")}

Status: {action.newStatus.replace('_', ' ').title()}
Claim ID: {claim_data.claim_id}

{action.statusNotes}

You can track your claim status using this link: {self.config.get('tracking_url', 'https://claims.rozie.ai')}/{claim_data.claim_id}

Best regards,
Claims Processing Team
            """
            
            await self.notification_service.send_email(
                to_email=claim_data.email_data.get('from_email'),
                subject=f"Claim Status Update - {claim_data.claim_id}",
                message=message
            )
            
        except Exception as e:
            logger.error(f"Error sending status update to customer: {e}")
    
    async def _send_error_notification(self, email_data: Dict[str, Any], error_message: str):
        """Send error notification"""
        try:
            await self.notification_service.send_email(
                to_email=email_data.get('from_email'),
                subject="Claim Processing Error",
                message=f"""
Dear {email_data.get('from_name', 'Valued Customer')},

We encountered an error while processing your claim. Our technical team has been notified and will resolve this issue shortly.

Error: {error_message}

Please try submitting your claim again, or contact our support team for assistance.

Best regards,
Claims Processing Team
                """
            )
            
            # Log error for technical team
            logger.error(f"Error notification sent for email from {email_data.get('from_email')}")
            
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")
    
    def _format_zendesk_description(self, claim_data: ClaimData) -> str:
        """Format claim data for Zendesk ticket description"""
        return f"""
Claim ID: {claim_data.claim_id}
Claim Type: {claim_data.classification.claimType}
Urgency: {claim_data.classification.urgency}

Customer Information:
- Name: {claim_data.email_data.get('from_name', 'N/A')}
- Email: {claim_data.email_data.get('from_email', 'N/A')}

Claim Details:
- Policy Number: {claim_data.extracted_details.policyNumber or 'N/A'}
- Claim Amount: ${claim_data.extracted_details.claimAmount or 'N/A'}
- Incident Date: {claim_data.extracted_details.incidentDate or 'N/A'}
- Location: {claim_data.extracted_details.location or 'N/A'}

AI Analysis:
- Severity: {claim_data.ai_analysis.classification.severity}
- Estimated Value: ${claim_data.ai_analysis.classification.estimatedValue}
- Fraud Risk Score: {claim_data.ai_analysis.fraudAnalysis.riskScore}/100
- Overall Recommendation: {claim_data.ai_analysis.overallRecommendation}

Original Email Subject: {claim_data.email_data.get('subject', 'N/A')}
        """
    
    def _map_priority(self, priority: str) -> str:
        """Map AI priority to Zendesk priority"""
        mapping = {
            "low": "low",
            "medium": "normal",
            "high": "high",
            "urgent": "urgent"
        }
        return mapping.get(priority, "normal")
    
    async def _monitor_active_claims(self):
        """Monitor active claims for timeouts and escalations"""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                for claim_id, claim_data in list(self.active_claims.items()):
                    # Check for timeouts
                    if current_time - claim_data.updated_at > timedelta(hours=4):
                        await self._escalate_timeout(claim_data)
                    
                    # Check for SLA violations
                    if current_time - claim_data.created_at > timedelta(hours=4):
                        await self._escalate_sla_violation(claim_data)
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in active claims monitor: {e}")
                await asyncio.sleep(60)
    
    async def _escalate_timeout(self, claim_data: ClaimData):
        """Escalate claim due to timeout"""
        try:
            logger.warning(f"Escalating claim {claim_data.claim_id} due to timeout")
            
            # Send escalation notification
            await self.slack_integration.send_escalation_notification(claim_data, "timeout")
            
            # Update status
            claim_data.status = "escalated"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
        except Exception as e:
            logger.error(f"Error escalating timeout: {e}")
    
    async def _escalate_sla_violation(self, claim_data: ClaimData):
        """Escalate claim due to SLA violation"""
        try:
            logger.warning(f"Escalating claim {claim_data.claim_id} due to SLA violation")
            
            # Send SLA violation notification
            await self.slack_integration.send_escalation_notification(claim_data, "sla_violation")
            
            # Update status
            claim_data.status = "sla_violation"
            claim_data.updated_at = datetime.now()
            await self.database.update_claim(claim_data)
            
        except Exception as e:
            logger.error(f"Error escalating SLA violation: {e}")
    
    def _dict_to_claim_data(self, claim_dict: Dict[str, Any]) -> ClaimData:
        """Convert database dictionary to ClaimData object"""
        try:
            # Create default objects for missing data
            default_classification = EmailClassification(
                isClaim=True,
                claimType=claim_dict.get('classification', {}).get('claim_type', 'general'),
                urgency=claim_dict.get('classification', {}).get('urgency', 'medium'),
                requiresImmediateResponse=False,
                suggestedResponse=""
            )

            # Create default objects with required parameters
            default_extraction = DocumentExtraction(
                policyNumber="",
                claimAmount=0.0,
                incidentDate="",
                location="",
                partiesInvolved=[],
                damages="",
                witnesses=[],
                policeReportNumber="",
                medicalInformation=""
            )
            default_analysis = ClaimAnalysis()

            # Parse datetime strings
            created_at = datetime.fromisoformat(claim_dict['created_at'].replace('Z', '+00:00')) if isinstance(claim_dict.get('created_at'), str) else claim_dict.get('created_at', datetime.now())
            updated_at = datetime.fromisoformat(claim_dict['updated_at'].replace('Z', '+00:00')) if isinstance(claim_dict.get('updated_at'), str) else claim_dict.get('updated_at', datetime.now())

            return ClaimData(
                claim_id=claim_dict.get('claim_number', claim_dict.get('id', 'UNKNOWN')),
                email_data=claim_dict.get('email_data', {}),
                classification=default_classification,
                extracted_details=default_extraction,
                ai_analysis=default_analysis,
                status=claim_dict.get('status', 'received'),
                created_at=created_at,
                updated_at=updated_at,
                zendesk_ticket_id=claim_dict.get('zendesk_ticket_id'),
                assigned_agent=claim_dict.get('assigned_agent_id'),
                decision=claim_dict.get('decision')
            )
        except Exception as e:
            logger.error(f"Error converting dict to ClaimData: {e}")
            # Return a minimal valid ClaimData object
            return ClaimData(
                claim_id=claim_dict.get('claim_number', 'ERROR'),
                email_data={},
                classification=EmailClassification(isClaim=True, claimType='general', urgency='medium', requiresImmediateResponse=False, suggestedResponse=""),
                extracted_details=DocumentExtraction(
                    policyNumber="",
                    claimAmount=0.0,
                    incidentDate="",
                    location="",
                    partiesInvolved=[],
                    damages="",
                    witnesses=[],
                    policeReportNumber="",
                    medicalInformation=""
                ),
                ai_analysis=ClaimAnalysis(),
                status='received',
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

    async def _process_pending_claims(self):
        """Process any pending claims in the database"""
        while self.is_running:
            try:
                pending_claims_dicts = await self.database.get_pending_claims()

                for claim_dict in pending_claims_dicts:
                    claim_data = self._dict_to_claim_data(claim_dict)
                    if claim_data.claim_id not in self.active_claims:
                        self.active_claims[claim_data.claim_id] = claim_data
                        await self._determine_human_action(claim_data)

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"Error processing pending claims: {e}")
                await asyncio.sleep(60)