###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
import baml_py
from enum import Enum

from pydantic import BaseModel, ConfigDict

from typing_extensions import TypeAlias, Literal
from typing import Dict, Generic, List, Optional, TypeVar, Union

from . import types
from .types import Checked, Check

###############################################################################
#
#  These types are used for streaming, for when an instance of a type
#  is still being built up and any of its fields is not yet fully available.
#
###############################################################################

T = TypeVar('T')
class StreamState(BaseModel, Generic[T]):
    value: T
    state: Literal["Pending", "Incomplete", "Complete"]


class AgentAssignment(BaseModel):
    intent: Optional[Literal["assign_agent"]] = None
    claimId: Optional[str] = None
    agentType: Optional[Union[Literal["claims_adjuster"], Literal["senior_adjuster"], Literal["fraud_investigator"], Literal["manager"], Literal["legal_counsel"]]] = None
    priority: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]] = None
    reason: Optional[str] = None
    estimatedProcessingTime: Optional[str] = None

class AgentDecision(BaseModel):
    intent: Optional[Literal["agent_decision"]] = None
    claimId: Optional[str] = None
    decision: Optional[Union[Literal["approve"], Literal["deny"], Literal["investigate"], Literal["request_more_info"], Literal["escalate"]]] = None
    settlementAmount: Optional[float] = None
    reasoning: Optional[str] = None
    nextSteps: List[str]
    requiresManagerApproval: Optional[bool] = None

class AgentResponse(BaseModel):
    agentId: Optional[str] = None
    responseType: Optional[Union[Literal["approval"], Literal["denial"], Literal["request_info"], Literal["escalation"], Literal["status_update"]]] = None
    decision: Optional[str] = None
    reasoning: Optional[str] = None
    nextActions: List[str]
    confidenceLevel: Optional[Union[Literal["low"], Literal["medium"], Literal["high"]]] = None

class ClaimAnalysis(BaseModel):
    classification: Optional["ClaimClassification"] = None
    fraudAnalysis: Optional["FraudAnalysis"] = None
    coverageAnalysis: Optional["CoverageAnalysis"] = None
    documents: List["DocumentAnalysis"]
    liabilityAssessment: Optional["LiabilityAssessment"] = None
    overallRecommendation: Optional[Union[Literal["approve"], Literal["deny"], Literal["investigate"], Literal["request_more_info"]]] = None
    reasoning: Optional[str] = None
    nextSteps: List[str]
    priority: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]] = None

class ClaimClassification(BaseModel):
    claimType: Optional[Union[Literal["auto"], Literal["property"], Literal["liability"], Literal["professional"], Literal["bodily_injury"]]] = None
    severity: Optional[Union[Literal["minor"], Literal["major"], Literal["catastrophic"]]] = None
    estimatedValue: Optional[float] = None
    confidenceScore: Optional[float] = None

class CoverageAnalysis(BaseModel):
    policyValid: Optional[bool] = None
    coverageType: Optional[str] = None
    deductible: Optional[float] = None
    coverageLimit: Optional[float] = None
    exclusions: List[str]
    recommendation: Optional[Union[Literal["covered"], Literal["partially_covered"], Literal["not_covered"]]] = None

class CustomerCommunication(BaseModel):
    intent: Optional[Literal["customer_communication"]] = None
    claimId: Optional[str] = None
    communicationType: Optional[Union[Literal["acknowledgment"], Literal["status_update"], Literal["decision_notification"], Literal["document_request"]]] = None
    message: Optional[str] = None
    attachments: List[str]
    urgency: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]] = None

class DocumentAnalysis(BaseModel):
    documentType: Optional[Union[Literal["police_report"], Literal["medical_record"], Literal["receipt"], Literal["photo"], Literal["insurance_certificate"], Literal["other"]]] = None
    extractedData: Dict[str, Optional[str]]
    completeness: Optional[float] = None
    quality: Optional[float] = None
    requiredForClaim: Optional[bool] = None

class DocumentExtraction(BaseModel):
    policyNumber: Optional[str] = None
    claimAmount: Optional[float] = None
    incidentDate: Optional[str] = None
    location: Optional[str] = None
    partiesInvolved: List[str]
    damages: List[str]
    witnesses: List[str]
    policeReportNumber: Optional[str] = None
    medicalInformation: List[str]

class DocumentProcessing(BaseModel):
    intent: Optional[Literal["process_documents"]] = None
    claimId: Optional[str] = None
    documentType: Optional[Union[Literal["police_report"], Literal["medical_record"], Literal["receipt"], Literal["photo"], Literal["insurance_certificate"], Literal["other"]]] = None
    processingAction: Optional[Union[Literal["ocr"], Literal["classify"], Literal["validate"], Literal["extract_data"]]] = None
    priority: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]] = None
    expectedOutput: Optional[str] = None

class EmailClassification(BaseModel):
    isClaim: Optional[bool] = None
    claimType: Optional[Union[Literal["auto"], Literal["property"], Literal["liability"], Literal["professional"], Literal["bodily_injury"], Literal["general_inquiry"], Literal["other"]]] = None
    urgency: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]] = None
    requiresImmediateResponse: Optional[bool] = None
    suggestedResponse: Optional[str] = None

class EscalateClaim(BaseModel):
    intent: Optional[Literal["escalate_claim"]] = None
    claimId: Optional[str] = None
    escalationReason: Optional[str] = None
    escalationLevel: Optional[Union[Literal["manager"], Literal["director"], Literal["legal"], Literal["fraud_team"]]] = None
    urgency: Optional[Union[Literal["high"], Literal["urgent"]]] = None
    additionalContext: Optional[str] = None

class FraudAnalysis(BaseModel):
    riskScore: Optional[int] = None
    redFlags: List[str]
    fraudIndicators: List[str]
    recommendation: Optional[Union[Literal["approve"], Literal["investigate"], Literal["deny"]]] = None
    confidenceScore: Optional[float] = None

class FraudInvestigation(BaseModel):
    intent: Optional[Literal["fraud_investigation"]] = None
    claimId: Optional[str] = None
    investigationType: Optional[Union[Literal["preliminary"], Literal["detailed"], Literal["surveillance"], Literal["background_check"]]] = None
    redFlags: List[str]
    investigationSteps: List[str]
    estimatedDuration: Optional[str] = None
    requiresExternalResources: Optional[bool] = None

class LiabilityAssessment(BaseModel):
    faultDetermination: Optional[Union[Literal["clear_liability"], Literal["shared_liability"], Literal["no_liability"], Literal["unclear"]]] = None
    faultPercentage: Optional[float] = None
    contributingFactors: List[str]
    evidenceStrength: Optional[Union[Literal["strong"], Literal["moderate"], Literal["weak"]]] = None
    recommendation: Optional[str] = None

class RequestMoreInformation(BaseModel):
    intent: Optional[Literal["request_more_info"]] = None
    claimId: Optional[str] = None
    requestedDocuments: List[str]
    questions: List[str]
    deadline: Optional[str] = None
    urgency: Optional[Union[Literal["low"], Literal["medium"], Literal["high"], Literal["urgent"]]] = None

class TeamNotification(BaseModel):
    intent: Optional[Literal["team_notification"]] = None
    claimId: Optional[str] = None
    notificationType: Optional[Union[Literal["new_claim"], Literal["agent_assigned"], Literal["decision_made"], Literal["escalation"], Literal["urgent_alert"]]] = None
    channel: Optional[Union[Literal["slack"], Literal["email"], Literal["both"]]] = None
    message: Optional[str] = None
    recipients: List[str]
    requiresAction: Optional[bool] = None

class UpdateClaimStatus(BaseModel):
    intent: Optional[Literal["update_status"]] = None
    claimId: Optional[str] = None
    newStatus: Optional[Union[Literal["received"], Literal["under_review"], Literal["pending_documents"], Literal["approved"], Literal["denied"], Literal["investigation"], Literal["closed"]]] = None
    statusNotes: Optional[str] = None
    notifyCustomer: Optional[bool] = None
    internalNotes: Optional[str] = None

class WorkflowCompletion(BaseModel):
    intent: Optional[Literal["workflow_complete"]] = None
    claimId: Optional[str] = None
    finalStatus: Optional[Union[Literal["approved"], Literal["denied"], Literal["closed"], Literal["referred"]]] = None
    summary: Optional[str] = None
    lessonsLearned: List[str]
    recommendations: List[str]
